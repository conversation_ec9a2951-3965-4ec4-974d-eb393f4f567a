'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Send,
  Calendar,
  MessageCircle
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email'),
  phone: z.string().min(8, 'Please enter a valid phone number'),
  service: z.string().min(1, 'Please select a service'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  preferredTime: z.string().optional(),
  studentAge: z.string().optional(),
});

type ContactFormData = z.infer<typeof contactSchema>;

const Contact = () => {
  const { t } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
  });

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);

    try {
      // Form is ready to submit - reCAPTCHA temporarily disabled for stability

      // Submit via API route with security token
      const response = await fetch('/api/submit-contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit contact form');
      }

      toast.success('Message sent successfully!');
      reset();
    } catch (error) {
      console.error('Error submitting contact form:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message. Please try again.';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };



  const contactInfo = [
    {
      icon: MapPin,
      title: t('contact.location'),
      details: [t('contact.locationDetails1'), t('contact.locationDetails2')],
    },
    {
      icon: Phone,
      title: t('contact.phone'),
      details: [t('contact.phoneDetails1'), t('contact.phoneDetails2')],
    },
    {
      icon: Mail,
      title: t('contact.email'),
      details: [t('contact.emailDetails1'), t('contact.emailDetails2')],
    },
    {
      icon: Clock,
      title: t('contact.hours'),
      details: [t('contact.hoursDetails1'), t('contact.hoursDetails2')],
    },
  ];

  const services = [
    t('services.individual.title'),
    t('services.group.title'),
    t('services.online.title'),
    t('services.tajweed.title'),
    t('services.children.title'),
    t('services.intensive.title'),
    'Free Consultation', // Keep this as is since it's not in translations yet
  ];

  return (
    <section id="contact" className="py-24 bg-slate-50 dark:bg-slate-800 transition-colors duration-300 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 star-crescent opacity-20 dark:opacity-0"></div>

      {/* Dark Mode Islamic Pattern */}
      <div className="absolute inset-0 opacity-0 dark:opacity-15 transition-opacity duration-300">
        <div className="absolute inset-0 star-crescent"></div>
        <div className="absolute top-1/4 right-1/4 w-1/3 h-1/3 islamic-pattern opacity-40"></div>
        <div className="absolute bottom-1/4 left-1/4 w-1/4 h-1/4 mosque-silhouette opacity-30"></div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-20 right-20 opacity-5 dark:opacity-8">
        <img src="/images/islamic-pattern.svg" alt="" className="w-32 h-32" />
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-8 animate-fade-in-down">
            <span>Get In Touch</span>
          </div>
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-slate-100 mb-6 leading-tight animate-fade-in-up animate-stagger-1 text-professional-lg">
            Start Your Quran <span className="gradient-text-elegant">Journey Today</span>
          </h2>
          <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animate-stagger-2">
            Ready to begin learning? Get in touch to schedule your first class or ask any questions.
          </p>
        </div>

        <div className="grid lg:grid-cols-12 gap-16">
          {/* Contact Form */}
          <div className="lg:col-span-7 bg-white dark:bg-slate-800 rounded-3xl p-10 shadow-xl border border-slate-200 dark:border-slate-700 hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 animate-fade-in-left animate-stagger-3">
            <div className="flex items-center space-x-4 mb-10">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-green-600 rounded-2xl flex items-center justify-center shadow-lg animate-rotate-in animate-stagger-4">
                <MessageCircle className="w-8 h-8 text-white" />
              </div>
              <div>
                <h3 className="text-3xl font-bold text-slate-900 dark:text-slate-100">{t('contact.sendMessage')}</h3>
                <p className="text-slate-600 dark:text-slate-300 text-lg">{t('contact.respond')}</p>
              </div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
              {/* Name */}
              <div>
                <label className="block text-lg font-semibold text-slate-700 dark:text-slate-300 mb-3">
                  {t('contact.name')} *
                </label>
                <input
                  {...register('name')}
                  type="text"
                  className="w-full px-6 py-4 border-2 border-slate-200 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-100 rounded-2xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300 text-lg placeholder-slate-400 dark:placeholder-slate-500"
                  placeholder="Your full name"
                />
                {errors.name && (
                  <p className="mt-2 text-sm text-red-600 font-medium">{errors.name.message}</p>
                )}
              </div>

              {/* Email & Phone */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    {t('contact.email')} *
                  </label>
                  <input
                    {...register('email')}
                    type="email"
                    className="w-full px-4 py-3 border border-slate-300 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-100 rounded-lg focus:ring-2 focus:ring-emerald-600 focus:border-transparent transition-colors placeholder-slate-400 dark:placeholder-slate-500"
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    {t('contact.phone')} *
                  </label>
                  <input
                    {...register('phone')}
                    type="tel"
                    className="w-full px-4 py-3 border border-slate-300 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-100 rounded-lg focus:ring-2 focus:ring-emerald-600 focus:border-transparent transition-colors placeholder-slate-400 dark:placeholder-slate-500"
                    placeholder="+65 XXXX XXXX"
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
                  )}
                </div>
              </div>

              {/* Service & Student Age */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Service Interested *
                  </label>
                  <select
                    {...register('service')}
                    className="w-full px-4 py-3 border border-slate-300 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-100 rounded-lg focus:ring-2 focus:ring-emerald-600 focus:border-transparent transition-colors"
                  >
                    <option value="">Select a service</option>
                    {services.map((service) => (
                      <option key={service} value={service}>
                        {service}
                      </option>
                    ))}
                  </select>
                  {errors.service && (
                    <p className="mt-1 text-sm text-red-600">{errors.service.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Student Age (Optional)
                  </label>
                  <input
                    {...register('studentAge')}
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-100 rounded-lg focus:ring-2 focus:ring-emerald-600 focus:border-transparent transition-colors placeholder-slate-400 dark:placeholder-slate-500"
                    placeholder="e.g., 8 years old, Adult"
                  />
                </div>
              </div>

              {/* Preferred Time */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Preferred Time (Optional)
                </label>
                <input
                  {...register('preferredTime')}
                  type="text"
                  className="w-full px-4 py-3 border border-slate-300 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-100 rounded-lg focus:ring-2 focus:ring-emerald-600 focus:border-transparent transition-colors placeholder-slate-400 dark:placeholder-slate-500"
                  placeholder="e.g., Weekday evenings, Weekend mornings"
                />
              </div>

              {/* Message */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Message *
                </label>
                <textarea
                  {...register('message')}
                  rows={4}
                  className="w-full px-4 py-3 border border-slate-300 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-100 rounded-lg focus:ring-2 focus:ring-emerald-600 focus:border-transparent transition-colors resize-none placeholder-slate-400 dark:placeholder-slate-500"
                  placeholder="Tell us about your learning goals, current level, or any questions you have..."
                />
                {errors.message && (
                  <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
                )}
              </div>

              {/* Security Notice - Professional Version */}
              <div className="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">🔒</span>
                  </div>
                  <p className="text-sm text-emerald-700 dark:text-emerald-300">
                    <strong>Secure Form:</strong> Your message will be sent securely and we'll respond within 24 hours.
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full btn-professional text-white px-10 py-5 rounded-2xl font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed btn-hover-lift flex items-center justify-center space-x-3 text-professional focus-professional"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Sending...</span>
                  </>
                ) : (
                  <>
                    <Send className="w-6 h-6" />
                    <span>Send Message</span>
                  </>
                )}
              </button>
            </form>
          </div>

          {/* Contact Information */}
          <div className="lg:col-span-5 animate-fade-in-right animate-stagger-3">
            <div className="mb-12">
              <h3 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-6">Get in Touch</h3>
              <p className="text-xl text-slate-600 dark:text-slate-300 leading-relaxed">
                Have questions or ready to start your Quran learning journey?
                We're here to help you every step of the way.
              </p>
            </div>

            {/* Contact Details */}
            <div className="space-y-6 mb-8">
              {contactInfo.map((info, index) => (
                <div key={index} className={`flex items-start space-x-4 animate-fade-in-right animate-stagger-${index + 4}`}>
                  <div className="w-12 h-12 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center flex-shrink-0 hover:scale-110 transition-transform duration-300">
                    <info.icon className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-slate-900 dark:text-slate-100 mb-1">{info.title}</h4>
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-slate-600 dark:text-slate-400">{detail}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="bg-gradient-to-br from-emerald-50 to-green-50 dark:from-slate-800 dark:to-slate-700 rounded-xl p-6 card-hover-lift animate-scale-in animate-stagger-6">
              <h4 className="font-bold text-slate-900 dark:text-slate-100 mb-4">Quick Actions</h4>
              <div className="space-y-3">
                <a
                  href="tel:+6588352027"
                  className="flex items-center space-x-3 text-emerald-600 hover:text-emerald-700 transition-colors btn-hover-lift"
                >
                  <Phone className="w-5 h-5" />
                  <span>Call for immediate assistance</span>
                </a>
                <a
                  href="https://wa.me/6588352027"
                  className="flex items-center space-x-3 text-green-600 hover:text-green-700 transition-colors btn-hover-lift"
                >
                  <MessageCircle className="w-5 h-5" />
                  <span>WhatsApp for quick questions</span>
                </a>
                <a
                  href="#services"
                  className="flex items-center space-x-3 text-purple-600 hover:text-purple-700 transition-colors btn-hover-lift"
                >
                  <Calendar className="w-5 h-5" />
                  <span>View available services</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
