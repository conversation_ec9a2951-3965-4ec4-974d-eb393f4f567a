'use client';

import { useState, useEffect } from 'react';
import { BookOpen, RefreshCw, Share2 } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface HadithData {
  id: string;
  text: string;
  source: string;
  collection: string;
  book?: string;
  hadithNumber?: string;
  narrator?: string;
  grade?: string;
  language: 'en' | 'ar';
}

interface HadithResponse {
  success: boolean;
  data: HadithData;
  timestamp: string;
  cacheUntil: string;
  error?: string;
  message?: string;
}

const HadithOfTheWeek = () => {
  const { t } = useLanguage();
  const [hadith, setHadith] = useState<HadithData | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;
    const fetchHadith = async () => {
      try {
        const response = await fetch('/api/hadith?lang=en');
        if (!response.ok) {
          throw new Error('Failed to fetch hadith');
        }
        const result: HadithResponse = await response.json();

        if (result.success && result.data) {
          setHadith(result.data);
          localStorage.setItem('hadithOfTheWeek', JSON.stringify(result.data));
          localStorage.setItem('hadithFetchDate', new Date().toISOString());
        } else {
          throw new Error(result.message || 'Failed to fetch hadith');
        }
      } catch (error) {
        console.error("Error fetching hadith:", error);
      } finally {
        setLoading(false);
      }
    };

    const cachedHadith = localStorage.getItem('hadithOfTheWeek');
    const fetchDate = localStorage.getItem('hadithFetchDate');

    if (cachedHadith && fetchDate) {
      const oneWeek = 7 * 24 * 60 * 60 * 1000;
      if (new Date().getTime() - new Date(fetchDate).getTime() < oneWeek) {
        setHadith(JSON.parse(cachedHadith));
        setLoading(false);
        return;
      }
    }

    fetchHadith();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      const response = await fetch('/api/hadith?type=random');
      if (response.ok) {
        const result: HadithResponse = await response.json();
        if (result.success && result.data) {
          setHadith(result.data);
        }
      }
    } catch (error) {
      console.error("Error refreshing hadith:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleShare = async () => {
    if (!hadith) return;

    const shareText = `"${hadith.text}" - ${hadith.source}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Hadith of the Week',
          text: shareText,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(shareText);
        alert('Hadith copied to clipboard!');
      } catch (error) {
        console.log('Error copying to clipboard:', error);
      }
    }
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <section className="py-24 bg-white dark:bg-slate-900 transition-colors duration-300">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <BookOpen className="w-5 h-5" />
              <span>Hadith of the Week</span>
            </div>
          </div>
          <div className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8 shadow-lg border border-slate-200 dark:border-slate-700">
            <div className="text-center text-slate-600 dark:text-slate-400">
              <p>Loading...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="hadith" className="py-16 bg-white dark:bg-slate-900 transition-colors duration-300">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <BookOpen className="w-5 h-5" />
            <span>{t('hadith.title')}</span>
          </div>
        </div>
        <div className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8 shadow-lg border border-slate-200 dark:border-slate-700">
          {loading ? (
            <div className="text-center text-slate-600 dark:text-slate-400">
              <p>{t('common.loading')}</p>
            </div>
          ) : hadith ? (
            <blockquote className="text-center">
              <p className="text-xl lg:text-2xl text-slate-800 dark:text-slate-200 mb-6 leading-relaxed italic">
                &ldquo;{hadith.text}&rdquo;
              </p>
              <footer className="text-slate-600 dark:text-slate-400 font-medium">
                <div className="space-y-1">
                  <div>{hadith.source}</div>
                  {hadith.narrator && (
                    <div className="text-sm opacity-75">Narrated by: {hadith.narrator}</div>
                  )}
                  {hadith.grade && (
                    <div className="text-sm opacity-75">Grade: {hadith.grade}</div>
                  )}
                </div>
              </footer>
            </blockquote>
          ) : (
            <div className="text-center text-red-500 dark:text-red-400">
              <p>{t('common.error')}</p>
            </div>
          )}

          {/* Action Buttons */}
          {hadith && (
            <div className="flex justify-center space-x-4 mt-8">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center space-x-2 px-4 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white rounded-lg transition-colors duration-200"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                <span>{refreshing ? 'Loading...' : 'New Hadith'}</span>
              </button>

              <button
                onClick={handleShare}
                className="flex items-center space-x-2 px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors duration-200"
              >
                <Share2 className="w-4 h-4" />
                <span>Share</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default HadithOfTheWeek;