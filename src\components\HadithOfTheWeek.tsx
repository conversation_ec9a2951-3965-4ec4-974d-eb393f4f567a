'use client';

import { useState, useEffect } from 'react';
import { BookOpen, RefreshCw, Share2 } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';



interface HadithData {
  id: string;
  text: string;
  source: string;
  collection: string;
  book?: string;
  hadithNumber?: string;
  narrator?: string;
  grade?: string;
  language: 'en' | 'ar';
}

interface HadithResponse {
  success: boolean;
  data: HadithData;
  timestamp: string;
  cacheUntil: string;
  error?: string;
  message?: string;
}

// Fallback hadith in case API fails
const fallbackHadith: HadithData = {
  id: 'fallback',
  text: 'The Prophet (ﷺ) said: "The best of people are those who learn the Quran and teach it."',
  source: '<PERSON><PERSON><PERSON> <PERSON><PERSON> 5027',
  collection: '<PERSON><PERSON><PERSON> <PERSON><PERSON>',
  book: 'Virtues of the Quran',
  hadithNumber: '5027',
  narrator: '<PERSON><PERSON><PERSON>',
  grade: 'Sahih',
  language: 'en'
};

const HadithOfTheWeek = () => {
  const { t } = useLanguage();
  const [hadith, setHadith] = useState<HadithData | null>(fallbackHadith);
  const [refreshing, setRefreshing] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const fetchHadith = async () => {
      try {
        const response = await fetch('/api/hadith');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result: HadithResponse = await response.json();

        if (result.success && result.data) {
          setHadith(result.data);
        }
      } catch (error) {
        console.error("Error fetching hadith:", error);
        // Keep fallback hadith if API fails
      }
    };

    fetchHadith();
  }, []);

  // Prevent hydration mismatch by showing consistent content
  if (!mounted) {
    return (
      <section id="hadith" className="py-16 bg-white dark:bg-slate-900 transition-colors duration-300">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <BookOpen className="w-5 h-5" />
              <span>Hadith of the Week</span>
            </div>
          </div>
          <div className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8 shadow-lg border border-slate-200 dark:border-slate-700">
            <blockquote className="text-center">
              <p className="text-lg md:text-xl text-slate-700 dark:text-slate-300 leading-relaxed mb-6 italic">
                "{fallbackHadith.text}"
              </p>
              <footer className="text-sm text-slate-500 dark:text-slate-400">
                <cite>— {fallbackHadith.source}</cite>
              </footer>
            </blockquote>
          </div>
        </div>
      </section>
    );
  }

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      const response = await fetch('/api/hadith?type=random');
      if (response.ok) {
        const result: HadithResponse = await response.json();
        if (result.success && result.data) {
          setHadith(result.data);
        }
      }
    } catch (error) {
      console.error("Error refreshing hadith:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleShare = async () => {
    if (!hadith) return;

    const shareText = `"${hadith.text}" - ${hadith.source}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Hadith of the Week',
          text: shareText,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(shareText);
        alert('Hadith copied to clipboard!');
      } catch (error) {
        console.log('Error copying to clipboard:', error);
      }
    }
  };



  return (
    <section id="hadith" className="py-16 bg-white dark:bg-slate-900 transition-colors duration-300">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <BookOpen className="w-5 h-5" />
            <span>{t('hadith.title')}</span>
          </div>
        </div>
        <div className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8 shadow-lg border border-slate-200 dark:border-slate-700">
          <blockquote className="text-center">
            <p className="text-xl lg:text-2xl text-slate-800 dark:text-slate-200 mb-6 leading-relaxed italic">
              &ldquo;{hadith?.text || fallbackHadith.text}&rdquo;
            </p>
            <footer className="text-slate-600 dark:text-slate-400 font-medium">
              <div className="space-y-1">
                <div>{hadith?.source || fallbackHadith.source}</div>
                {(hadith?.narrator || fallbackHadith.narrator) && (
                  <div className="text-sm opacity-75">Narrated by: {hadith?.narrator || fallbackHadith.narrator}</div>
                )}
                {(hadith?.grade || fallbackHadith.grade) && (
                  <div className="text-sm opacity-75">Grade: {hadith?.grade || fallbackHadith.grade}</div>
                )}
              </div>
            </footer>
          </blockquote>

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4 mt-8">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2 px-4 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white rounded-lg transition-colors duration-200"
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>{refreshing ? 'Loading...' : 'New Hadith'}</span>
            </button>

            <button
              onClick={handleShare}
              className="flex items-center space-x-2 px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors duration-200"
            >
              <Share2 className="w-4 h-4" />
              <span>Share</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HadithOfTheWeek;