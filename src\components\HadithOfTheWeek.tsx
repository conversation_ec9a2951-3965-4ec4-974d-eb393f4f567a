'use client';

import { useState, useEffect } from 'react';
import { BookOpen } from 'lucide-react';
import { useTranslations } from 'next-intl';

interface Hadith {
  hadith: [
    {
      lang: string;
      chapterNumber: string;
      chapterTitle: string;
      urn: number;
      body: string;
    }
  ];
  collection: string;
  book: string;
  hadithNumber: string;
  source: string;
}

const HadithOfTheWeek = () => {
  const t = useTranslations();
  const [hadith, setHadith] = useState<Hadith | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;
    const fetchHadith = async () => {
      try {
        const response = await fetch('https://api.sunnah.com/v1/hadiths/random');
        if (!response.ok) {
          throw new Error('Failed to fetch hadith');
        }
        const data = await response.json();
        
        const hadithData = {
          hadith: data.hadith,
          collection: data.collection,
          book: data.book,
          hadithNumber: data.hadithNumber,
          source: `(${data.collection} ${data.hadithNumber})`
        };

        setHadith(hadithData);
        localStorage.setItem('hadithOfTheWeek', JSON.stringify(hadithData));
        localStorage.setItem('hadithFetchDate', new Date().toISOString());
      } catch (error) {
        console.error("Error fetching hadith:", error);
      } finally {
        setLoading(false);
      }
    };

    const cachedHadith = localStorage.getItem('hadithOfTheWeek');
    const fetchDate = localStorage.getItem('hadithFetchDate');

    if (cachedHadith && fetchDate) {
      const oneWeek = 7 * 24 * 60 * 60 * 1000;
      if (new Date().getTime() - new Date(fetchDate).getTime() < oneWeek) {
        setHadith(JSON.parse(cachedHadith));
        setLoading(false);
        return;
      }
    }

    fetchHadith();
  }, []);

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <section className="py-24 bg-white dark:bg-slate-900 transition-colors duration-300">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <BookOpen className="w-5 h-5" />
              <span>Hadith of the Week</span>
            </div>
          </div>
          <div className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8 shadow-lg border border-slate-200 dark:border-slate-700">
            <div className="text-center text-slate-600 dark:text-slate-400">
              <p>Loading...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="hadith" className="py-16 bg-white dark:bg-slate-900 transition-colors duration-300">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <BookOpen className="w-5 h-5" />
            <span>{t('hadith.title')}</span>
          </div>
        </div>
        <div className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8 shadow-lg border border-slate-200 dark:border-slate-700">
          {loading ? (
            <div className="text-center text-slate-600 dark:text-slate-400">
              <p>{t('common.loading')}</p>
            </div>
          ) : hadith ? (
            <blockquote className="text-center">
              <p className="text-xl lg:text-2xl text-slate-800 dark:text-slate-200 mb-6 leading-relaxed italic">
                &ldquo;{hadith.hadith[0].body}&rdquo;
              </p>
              <footer className="text-slate-600 dark:text-slate-400 font-medium">
                {hadith.source}
              </footer>
            </blockquote>
          ) : (
            <div className="text-center text-red-500 dark:text-red-400">
              <p>{t('common.error')}</p>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default HadithOfTheWeek;