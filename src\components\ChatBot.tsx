'use client';

import { useState, useRef, useEffect } from 'react';
import { MessageCircle, X, Send, Bot, User } from 'lucide-react';

interface Message {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  options?: string[];
}

const ChatBot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      // Initial greeting
      setTimeout(() => {
        addBotMessage(
          "<PERSON><PERSON><PERSON><PERSON>! 👋 I'm here to help you learn about our Quran classes. How can I assist you today?",
          [
            "Tell me about classes",
            "Pricing information",
            "Schedule a consultation",
            "Contact details"
          ]
        );
      }, 500);
    }
  }, [isOpen]);

  const addBotMessage = (text: string, options?: string[]) => {
    const message: Message = {
      id: Date.now().toString(),
      text,
      isBot: true,
      timestamp: new Date(),
      options
    };
    setMessages(prev => [...prev, message]);
  };

  const addUserMessage = (text: string) => {
    const message: Message = {
      id: Date.now().toString(),
      text,
      isBot: false,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
  };

  const handleBotResponse = (userMessage: string) => {
    setIsTyping(true);
    
    setTimeout(() => {
      setIsTyping(false);
      
      const lowerMessage = userMessage.toLowerCase();
      
      if (lowerMessage.includes('class') || lowerMessage.includes('service')) {
        addBotMessage(
          "We offer several types of Quran classes:\n\n• Individual Classes (1-on-1)\n• Group Classes (3-5 students)\n• Online Classes\n• Tajweed Mastery\n• Children's Program\n• Intensive Courses\n\nWhich type interests you most?",
          ["Individual Classes", "Group Classes", "Online Classes", "Children's Program"]
        );
      } else if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('fee')) {
        addBotMessage(
          "Here are our class rates:\n\n• Individual: From $80/session (60 min)\n• Group: From $45/session (90 min)\n• Online: From $60/session (60 min)\n• Children's: From $50/session (45 min)\n\nWould you like to schedule a free consultation to discuss your needs?",
          ["Schedule consultation", "Tell me more about individual classes", "Ask about group discounts"]
        );
      } else if (lowerMessage.includes('schedule') || lowerMessage.includes('consultation') || lowerMessage.includes('appointment')) {
        addBotMessage(
          "Great! I'd be happy to help you schedule a free consultation with Ustazah Norazah.\n\nPlease provide:\n• Your name\n• Phone number\n• Preferred time\n• Student's age (if applicable)\n\nOr you can call us directly at +65 XXXX XXXX",
          ["Call now", "Fill contact form", "WhatsApp us"]
        );
      } else if (lowerMessage.includes('contact') || lowerMessage.includes('phone') || lowerMessage.includes('email')) {
        addBotMessage(
          "Here's how to reach us:\n\n📞 Phone: +65 XXXX XXXX\n📧 Email: <EMAIL>\n📍 Location: Punggol, Singapore\n💬 WhatsApp: Available\n\nTeaching Hours:\nMon-Fri: 2PM - 8PM\nSat-Sun: 9AM - 6PM",
          ["Call now", "Send WhatsApp", "Visit contact page"]
        );
      } else if (lowerMessage.includes('individual')) {
        addBotMessage(
          "Individual Classes offer:\n\n✅ Personalized learning plan\n✅ Flexible scheduling\n✅ One-on-one attention\n✅ Progress tracking\n✅ Customized pace\n\nRate: From $80/session (60 minutes)\n\nWould you like to book a trial class?",
          ["Book trial class", "Ask about scheduling", "Learn about other classes"]
        );
      } else if (lowerMessage.includes('group')) {
        addBotMessage(
          "Group Classes feature:\n\n✅ Small class sizes (3-5 students)\n✅ Interactive learning\n✅ Peer motivation\n✅ Cost-effective\n✅ Social learning environment\n\nRate: From $45/session (90 minutes)\n\nInterested in joining a group?",
          ["Join a group", "Check group availability", "Compare with individual"]
        );
      } else if (lowerMessage.includes('online')) {
        addBotMessage(
          "Online Classes provide:\n\n✅ Learn from anywhere\n✅ Digital resources\n✅ Recorded sessions\n✅ Technical support\n✅ Same quality as in-person\n\nRate: From $60/session (60 minutes)\n\nReady to start online learning?",
          ["Start online classes", "Technical requirements", "Schedule online trial"]
        );
      } else if (lowerMessage.includes('children') || lowerMessage.includes('kids')) {
        addBotMessage(
          "Children's Program includes:\n\n✅ Age-appropriate methods\n✅ Interactive activities\n✅ Reward system\n✅ Parent progress reports\n✅ Fun learning environment\n\nRate: From $50/session (45 minutes)\nAges: 4-16 years\n\nWhat's your child's age?",
          ["Book children's class", "Age 4-7", "Age 8-12", "Age 13-16"]
        );
      } else {
        addBotMessage(
          "I'd be happy to help you with that! Here are some things I can assist you with:",
          [
            "Class information",
            "Pricing details",
            "Schedule consultation",
            "Contact information",
            "Speak to Ustazah Norazah"
          ]
        );
      }
    }, 1500); // Fixed delay instead of random
  };

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      addUserMessage(inputValue);
      handleBotResponse(inputValue);
      setInputValue('');
    }
  };

  const handleOptionClick = (option: string) => {
    addUserMessage(option);
    handleBotResponse(option);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Button */}
      <button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-8 right-8 w-20 h-20 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white rounded-full shadow-2xl hover:from-emerald-700 hover:to-emerald-800 transition-all duration-300 z-40 flex items-center justify-center hover:scale-110 ${
          isOpen ? 'scale-0' : 'scale-100'
        }`}
      >
        <MessageCircle className="w-10 h-10" />
        <div className="absolute -top-2 -right-2 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center animate-pulse">
          <span className="text-sm font-bold">!</span>
        </div>
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-8 right-8 w-96 h-[600px] bg-white rounded-3xl shadow-2xl z-50 flex flex-col border border-slate-200">
          {/* Header */}
          <div className="bg-gradient-to-r from-emerald-600 to-emerald-700 text-white p-6 rounded-t-3xl flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                <Bot className="w-7 h-7" />
              </div>
              <div>
                <h3 className="font-bold text-lg">Quran Learning Assistant</h3>
                <p className="text-emerald-100 font-medium">Usually replies instantly</p>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="w-10 h-10 hover:bg-emerald-500 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.isBot ? 'justify-start' : 'justify-end'}`}>
                <div className={`max-w-[80%] ${message.isBot ? 'bg-gray-100' : 'bg-emerald-600 text-white'} rounded-2xl p-3`}>
                  <div className="flex items-start space-x-2">
                    {message.isBot && (
                      <Bot className="w-4 h-4 text-emerald-600 mt-1 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      <p className="text-sm whitespace-pre-line">{message.text}</p>
                      {message.options && (
                        <div className="mt-3 space-y-2">
                          {message.options.map((option, index) => (
                            <button
                              key={index}
                              onClick={() => handleOptionClick(option)}
                              className="block w-full text-left text-xs bg-white text-emerald-600 border border-emerald-200 rounded-lg px-3 py-2 hover:bg-emerald-50 transition-colors"
                            >
                              {option}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                    {!message.isBot && (
                      <User className="w-4 h-4 text-white mt-1 flex-shrink-0" />
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-2xl p-3">
                  <div className="flex items-center space-x-2">
                    <Bot className="w-4 h-4 text-emerald-600" />
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-600 focus:border-transparent text-sm"
              />
              <button
                onClick={handleSendMessage}
                className="w-10 h-10 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors flex items-center justify-center"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatBot;
