'use client';

import { supabase } from '@/lib/supabase';
import { useLanguage } from '@/contexts/LanguageContext';
import { Calendar, Clock, Tag } from 'lucide-react';
import { useEffect, useState } from 'react';
import Link from 'next/link';

interface Timeslot {
  id: string;
  day_of_week: string;
  start_time: string;
  end_time: string;
  is_booked: boolean;
}

async function getTimeslots() {
  const { data, error } = await supabase
    .from('timeslots')
    .select('*')
    .order('day_of_week', { ascending: true })
    .order('start_time', { ascending: true });

  if (error) {
    console.error('Error fetching timeslots:', error);
    return [];
  }
  return data;
}

const Availability = () => {
  const { t } = useLanguage();
  const [timeslots, setTimeslots] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTimeslots = async () => {
      try {
        const data = await getTimeslots();
        setTimeslots(data);
      } catch (error) {
        console.error('Error fetching timeslots:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTimeslots();
  }, []);

  const groupedTimeslots = timeslots.reduce((acc, slot) => {
    const day = slot.day_of_week;
    if (!acc[day]) {
      acc[day] = [];
    }
    acc[day].push(slot);
    return acc;
  }, {} as Record<string, Timeslot[]>);

  const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

  if (loading) {
    return (
      <section id="availability" className="py-24 bg-slate-50 dark:bg-slate-800 transition-colors duration-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-slate-600 dark:text-slate-400">Loading availability...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="availability" className="py-24 bg-slate-50 dark:bg-slate-800 transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <Calendar className="w-5 h-5" />
            <span>{t('availability.title')}</span>
          </div>
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-slate-100 mb-6">
            {t('availability.subtitle')}
          </h2>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          <div className="lg:col-span-2">
            <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-8">
              {daysOfWeek.map(day => (
                <div key={day} className="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-slate-100 mb-4 text-center">{day}</h3>
                  <div className="space-y-3">
                    {groupedTimeslots[day] ? (
                      groupedTimeslots[day].map((slot: Timeslot) => (
                        <div
                          key={slot.id}
                          className={`p-3 rounded-lg text-center font-medium ${
                            slot.is_booked
                              ? 'bg-slate-200 dark:bg-slate-600 text-slate-500 dark:text-slate-400 line-through'
                              : 'bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-300'
                          }`}
                        >
                          {new Date(`1970-01-01T${slot.start_time}Z`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', timeZone: 'UTC' })}
                          {' - '}
                          {new Date(`1970-01-01T${slot.end_time}Z`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', timeZone: 'UTC' })}
                        </div>
                      ))
                    ) : (
                      <p className="text-slate-500 dark:text-slate-400 text-center italic">No slots</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-8">
            <div className="flex items-center space-x-3 mb-6">
              <Tag className="w-6 h-6 text-emerald-500" />
              <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100">{t('availability.pricing_title')}</h3>
            </div>
            <div className="space-y-4 mb-8">
              <div className="flex justify-between items-center p-4 bg-slate-100 dark:bg-slate-600 rounded-lg">
                <span className="font-semibold text-slate-800 dark:text-slate-200">1 Hour Session</span>
                <span className="text-lg font-bold text-emerald-600 dark:text-emerald-400">{t('availability.price_hourly')}</span>
              </div>
              <div className="flex justify-between items-center p-4 bg-slate-100 dark:bg-slate-600 rounded-lg">
                <span className="font-semibold text-slate-800 dark:text-slate-200">30 Minute Session</span>
                <span className="text-lg font-bold text-emerald-600 dark:text-emerald-400">{t('availability.price_half_hour')}</span>
              </div>
            </div>
            <Link
              href="#contact"
              className="w-full flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-colors duration-300 bg-emerald-600 text-white hover:bg-emerald-700"
            >
              <span>{t('availability.book_now')}</span>
              <Clock className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Availability;