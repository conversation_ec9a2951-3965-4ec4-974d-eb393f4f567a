<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="islamicGeometric" patternUnits="userSpaceOnUse" width="50" height="50">
      <rect width="50" height="50" fill="#F8FAFC"/>
      <g stroke="#059669" stroke-width="0.5" fill="none">
        <!-- Octagonal star pattern -->
        <polygon points="25,5 35,15 35,35 25,45 15,35 15,15" opacity="0.3"/>
        <polygon points="25,10 30,20 30,30 25,40 20,30 20,20" opacity="0.5"/>
        <circle cx="25" cy="25" r="8" opacity="0.2"/>
        
        <!-- Connecting lines -->
        <line x1="0" y1="25" x2="50" y2="25" opacity="0.1"/>
        <line x1="25" y1="0" x2="25" y2="50" opacity="0.1"/>
        <line x1="0" y1="0" x2="50" y2="50" opacity="0.1"/>
        <line x1="50" y1="0" x2="0" y2="50" opacity="0.1"/>
      </g>
    </pattern>
    
    <radialGradient id="centerGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background pattern -->
  <rect width="200" height="200" fill="url(#islamicGeometric)"/>
  
  <!-- Central focal point -->
  <circle cx="100" cy="100" r="80" fill="url(#centerGlow)"/>
  
  <!-- Central star -->
  <g transform="translate(100,100)">
    <polygon points="0,-30 8,-8 30,0 8,8 0,30 -8,8 -30,0 -8,-8" 
             fill="#059669" opacity="0.2" stroke="#059669" stroke-width="1"/>
    <polygon points="0,-20 5,-5 20,0 5,5 0,20 -5,5 -20,0 -5,-5" 
             fill="#10B981" opacity="0.3" stroke="#10B981" stroke-width="1"/>
    <circle cx="0" cy="0" r="8" fill="#059669" opacity="0.4"/>
  </g>
  
  <!-- Corner decorations -->
  <g stroke="#059669" stroke-width="1" fill="#059669" opacity="0.1">
    <circle cx="20" cy="20" r="5"/>
    <circle cx="180" cy="20" r="5"/>
    <circle cx="20" cy="180" r="5"/>
    <circle cx="180" cy="180" r="5"/>
  </g>
</svg>
