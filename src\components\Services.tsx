'use client';

import Link from 'next/link';
import { 
  User, 
  Users, 
  Monitor, 
  BookOpen, 
  Heart, 
  Clock, 
  CheckCircle, 
  ArrowRight 
} from 'lucide-react';

const Services = () => {
  const services = [
    {
      icon: User,
      title: 'Individual Classes',
      description: 'One-on-one personalized Quran lessons tailored to your learning pace and goals.',
      features: [
        'Customized learning plan',
        'Flexible scheduling',
        'Personal attention',
        'Progress tracking'
      ],
      price: 'From $80/session',
      duration: '60 minutes',
      popular: false,
    },
    {
      icon: Users,
      title: 'Group Classes',
      description: 'Learn with fellow students in small groups for a collaborative learning experience.',
      features: [
        'Small class sizes (3-5 students)',
        'Interactive learning',
        'Peer motivation',
        'Cost-effective'
      ],
      price: 'From $45/session',
      duration: '90 minutes',
      popular: true,
    },
    {
      icon: Monitor,
      title: 'Online Classes',
      description: 'Convenient online Quran lessons from the comfort of your home via video call.',
      features: [
        'Learn from anywhere',
        'Digital resources',
        'Recorded sessions',
        'Technical support'
      ],
      price: 'From $60/session',
      duration: '60 minutes',
      popular: false,
    },
    {
      icon: BookOpen,
      title: 'Tajweed Mastery',
      description: 'Specialized program focusing on proper pronunciation and recitation rules.',
      features: [
        'Detailed pronunciation guide',
        'Audio practice sessions',
        'Correction techniques',
        'Certification available'
      ],
      price: 'From $90/session',
      duration: '75 minutes',
      popular: false,
    },
    {
      icon: Heart,
      title: 'Children\'s Program',
      description: 'Fun and engaging Quran classes designed specifically for young learners.',
      features: [
        'Age-appropriate methods',
        'Interactive activities',
        'Reward system',
        'Parent progress reports'
      ],
      price: 'From $50/session',
      duration: '45 minutes',
      popular: false,
    },
    {
      icon: Clock,
      title: 'Intensive Courses',
      description: 'Accelerated learning programs for students with specific goals and timelines.',
      features: [
        'Fast-track learning',
        'Daily sessions available',
        'Goal-oriented approach',
        'Comprehensive materials'
      ],
      price: 'Custom pricing',
      duration: 'Flexible',
      popular: false,
    },
  ];

  return (
    <section id="services" className="py-24 bg-slate-50 dark:bg-slate-800 transition-colors duration-300 relative overflow-hidden">
      {/* Background Islamic Pattern */}
      <div className="absolute inset-0 islamic-geometric opacity-30 dark:opacity-0"></div>

      {/* Dark Mode Islamic Pattern */}
      <div className="absolute inset-0 opacity-0 dark:opacity-15 transition-opacity duration-300">
        <div className="absolute inset-0 star-crescent"></div>
        <div className="absolute top-0 right-0 w-1/3 h-1/3 islamic-pattern opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-1/4 h-1/4 mosque-silhouette opacity-40"></div>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6 animate-fade-in-down">
            <span>Our Services</span>
          </div>
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-slate-100 mb-6 leading-tight animate-fade-in-up animate-stagger-1">
            Quran Learning <span className="bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">Programs</span>
          </h2>
          <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animate-stagger-2">
            Choose from our comprehensive range of Quran education programs,
            designed to meet your unique learning needs and schedule.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {services.map((service, index) => (
            <div
              key={index}
              className={`group relative bg-white dark:bg-slate-800 rounded-3xl shadow-xl transition-all duration-500 p-8 border border-slate-200 dark:border-slate-700 hover:border-emerald-200 dark:hover:border-emerald-400 hover:shadow-2xl hover:-translate-y-2 animate-fade-in-up animate-stagger-${index + 3} ${
                service.popular ? 'ring-2 ring-emerald-500 scale-105 shadow-2xl' : ''
              }`}
            >
              {/* Popular Badge */}
              {service.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="btn-professional text-white px-6 py-2 rounded-full text-sm font-semibold shadow-professional-lg text-professional">
                    Most Popular
                  </span>
                </div>
              )}

              {/* Icon */}
              <div className="w-20 h-20 bg-gradient-to-br from-emerald-50 via-emerald-100 to-green-50 dark:from-emerald-900/30 dark:via-emerald-800/30 dark:to-green-900/30 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-professional border border-emerald-100 dark:border-emerald-800/30">
                <service.icon className="w-10 h-10 text-emerald-600 dark:text-emerald-400 drop-shadow-sm" />
              </div>

              {/* Content */}
              <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4 group-hover:text-emerald-600 transition-colors text-professional-lg">{service.title}</h3>
              <p className="text-slate-600 dark:text-slate-300 mb-8 leading-relaxed text-lg text-professional">{service.description}</p>

              {/* Features */}
              <ul className="space-y-4 mb-8">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                    </div>
                    <span className="text-slate-700 dark:text-slate-300 font-medium">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* Pricing */}
              <div className="border-t border-slate-200 dark:border-slate-600 pt-8">
                <div className="mb-6">
                  <div className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-1">{service.price}</div>
                  <div className="text-slate-600 dark:text-slate-400 font-medium">{service.duration}</div>
                </div>

                <Link
                  href="#contact"
                  className={`group w-full flex items-center justify-center space-x-2 px-8 py-4 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-1 ${
                    service.popular
                      ? 'bg-gradient-to-r from-emerald-600 to-emerald-700 text-white hover:from-emerald-700 hover:to-emerald-800 shadow-lg hover:shadow-xl'
                      : 'bg-slate-100 dark:bg-slate-600 text-slate-900 dark:text-slate-100 hover:bg-slate-200 dark:hover:bg-slate-500 shadow-md hover:shadow-lg'
                  }`}
                >
                  <span>Book Now</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-8 text-center animate-fade-in-up animate-stagger-6">
          <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4">
            Not sure which program is right for you?
          </h3>
          <p className="text-slate-600 dark:text-slate-300 mb-6">
            Schedule a free consultation to discuss your learning goals and find the perfect program. Or explore our educational blog for learning tips and guidance.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="#contact"
              className="inline-flex items-center space-x-2 bg-green-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200 btn-hover-lift"
            >
              <span>Free Consultation</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/blog"
              className="inline-flex items-center space-x-2 bg-emerald-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-colors duration-200 btn-hover-lift"
            >
              <BookOpen className="w-5 h-5" />
              <span>Learning Resources</span>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
