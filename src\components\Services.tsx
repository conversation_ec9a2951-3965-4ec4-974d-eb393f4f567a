'use client';

import Link from 'next/link';
import {
  User,
  Users,
  Monitor,
  BookOpen,
  Heart,
  Clock,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const Services = () => {
  const { t } = useLanguage();
  const services = [
    {
      icon: User,
      title: t('services.individual.title'),
      description: t('services.individual.description'),
      features: [
        t('services.individual.feature1'),
        t('services.individual.feature2'),
        t('services.individual.feature3'),
        t('services.individual.feature4')
      ],
      price: t('services.individual.price'),
      duration: t('services.individual.duration'),
      popular: false,
    },
    {
      icon: Users,
      title: t('services.group.title'),
      description: t('services.group.description'),
      features: [
        t('services.group.feature1'),
        t('services.group.feature2'),
        t('services.group.feature3'),
        t('services.group.feature4')
      ],
      price: t('services.group.price'),
      duration: t('services.group.duration'),
      popular: true,
    },
    {
      icon: Monitor,
      title: t('services.online.title'),
      description: t('services.online.description'),
      features: [
        t('services.online.feature1'),
        t('services.online.feature2'),
        t('services.online.feature3'),
        t('services.online.feature4')
      ],
      price: t('services.online.price'),
      duration: t('services.online.duration'),
      popular: false,
    },
    {
      icon: BookOpen,
      title: t('services.tajweed.title'),
      description: t('services.tajweed.description'),
      features: [
        t('services.tajweed.feature1'),
        t('services.tajweed.feature2'),
        t('services.tajweed.feature3'),
        t('services.tajweed.feature4')
      ],
      price: t('services.tajweed.price'),
      duration: t('services.tajweed.duration'),
      popular: false,
    },
    {
      icon: Heart,
      title: t('services.children.title'),
      description: t('services.children.description'),
      features: [
        t('services.children.feature1'),
        t('services.children.feature2'),
        t('services.children.feature3'),
        t('services.children.feature4')
      ],
      price: t('services.children.price'),
      duration: t('services.children.duration'),
      popular: false,
    },
    {
      icon: Clock,
      title: t('services.intensive.title'),
      description: t('services.intensive.description'),
      features: [
        t('services.intensive.feature1'),
        t('services.intensive.feature2'),
        t('services.intensive.feature3'),
        t('services.intensive.feature4')
      ],
      price: t('services.intensive.price'),
      duration: t('services.intensive.duration'),
      popular: false,
    },
  ];

  return (
    <section id="services" className="py-24 bg-slate-50 dark:bg-slate-800 transition-colors duration-300 relative overflow-hidden">
      {/* Background Islamic Pattern */}
      <div className="absolute inset-0 islamic-geometric opacity-30 dark:opacity-0"></div>

      {/* Dark Mode Islamic Pattern */}
      <div className="absolute inset-0 opacity-0 dark:opacity-15 transition-opacity duration-300">
        <div className="absolute inset-0 star-crescent"></div>
        <div className="absolute top-0 right-0 w-1/3 h-1/3 islamic-pattern opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-1/4 h-1/4 mosque-silhouette opacity-40"></div>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6 animate-fade-in-down">
            <span>{t('services.title')}</span>
          </div>
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-slate-100 mb-6 leading-tight animate-fade-in-up animate-stagger-1">
            {t('services.subtitle')}
          </h2>
          <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animate-stagger-2">
            {t('services.description')}
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {services.map((service, index) => (
            <div
              key={index}
              className={`group relative bg-white dark:bg-slate-800 rounded-3xl shadow-xl transition-all duration-500 p-8 border border-slate-200 dark:border-slate-700 hover:border-emerald-200 dark:hover:border-emerald-400 hover:shadow-2xl hover:-translate-y-2 animate-fade-in-up animate-stagger-${index + 3} ${
                service.popular ? 'ring-2 ring-emerald-500 scale-105 shadow-2xl' : ''
              }`}
            >
              {/* Popular Badge */}
              {service.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="btn-professional text-white px-6 py-2 rounded-full text-sm font-semibold shadow-professional-lg text-professional">
                    {t('services.mostPopular')}
                  </span>
                </div>
              )}

              {/* Icon */}
              <div className="w-20 h-20 bg-gradient-to-br from-emerald-50 via-emerald-100 to-green-50 dark:from-emerald-900/30 dark:via-emerald-800/30 dark:to-green-900/30 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-professional border border-emerald-100 dark:border-emerald-800/30">
                <service.icon className="w-10 h-10 text-emerald-600 dark:text-emerald-400 drop-shadow-sm" />
              </div>

              {/* Content */}
              <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4 group-hover:text-emerald-600 transition-colors text-professional-lg">{service.title}</h3>
              <p className="text-slate-600 dark:text-slate-300 mb-8 leading-relaxed text-lg text-professional">{service.description}</p>

              {/* Features */}
              <ul className="space-y-4 mb-8">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                    </div>
                    <span className="text-slate-700 dark:text-slate-300 font-medium">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* Pricing */}
              <div className="border-t border-slate-200 dark:border-slate-600 pt-8">
                <div className="mb-6">
                  <div className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-1">{service.price}</div>
                  <div className="text-slate-600 dark:text-slate-400 font-medium">{service.duration}</div>
                </div>

                <Link
                  href="#contact"
                  className={`group w-full flex items-center justify-center space-x-2 px-8 py-4 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-1 ${
                    service.popular
                      ? 'bg-gradient-to-r from-emerald-600 to-emerald-700 text-white hover:from-emerald-700 hover:to-emerald-800 shadow-lg hover:shadow-xl'
                      : 'bg-slate-100 dark:bg-slate-600 text-slate-900 dark:text-slate-100 hover:bg-slate-200 dark:hover:bg-slate-500 shadow-md hover:shadow-lg'
                  }`}
                >
                  <span>{t('services.bookNow')}</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-8 text-center animate-fade-in-up animate-stagger-6">
          <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4">
            {t('services.notSure')}
          </h3>
          <p className="text-slate-600 dark:text-slate-300 mb-6">
            {t('services.consultation')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="#contact"
              className="inline-flex items-center space-x-2 bg-green-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200 btn-hover-lift"
            >
              <span>{t('services.freeConsultation')}</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/blog"
              className="inline-flex items-center space-x-2 bg-emerald-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-colors duration-200 btn-hover-lift"
            >
              <BookOpen className="w-5 h-5" />
              <span>{t('services.learningResources')}</span>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
