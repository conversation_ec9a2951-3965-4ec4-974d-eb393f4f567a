import { Resend } from 'resend';
import { ContactLead } from './supabase';

// Initialize Resend with API key (only on server side)
const getResendClient = () => {
  const apiKey = process.env.RESEND_API_KEY;
  if (!apiKey) {
    console.warn('RESEND_API_KEY not found. Email notifications will be disabled.');
    return null;
  }
  return new Resend(apiKey);
};

// Email configuration
const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>';
const TO_EMAIL = process.env.NOTIFICATION_EMAIL || '<EMAIL>';

// Email templates
export const createNewLeadEmailTemplate = (lead: ContactLead) => {
  const submissionTime = lead.created_at 
    ? new Date(lead.created_at).toLocaleString('en-SG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Singapore'
      })
    : 'Just now';

  return {
    subject: `🚨 New Lead: ${lead.full_name} - ${lead.service}`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Lead Notification</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
            .header { background: linear-gradient(135deg, #059669, #10b981); color: white; padding: 30px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
            .header p { margin: 10px 0 0; opacity: 0.9; }
            .content { padding: 30px; }
            .lead-info { background: #f1f5f9; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .info-row { display: flex; margin-bottom: 12px; }
            .info-label { font-weight: 600; color: #475569; min-width: 120px; }
            .info-value { color: #1e293b; }
            .message-box { background: #ecfdf5; border-left: 4px solid #059669; padding: 15px; margin: 20px 0; border-radius: 0 8px 8px 0; }
            .actions { text-align: center; margin: 30px 0; }
            .btn { display: inline-block; padding: 12px 24px; margin: 0 10px; text-decoration: none; border-radius: 8px; font-weight: 600; transition: all 0.2s; }
            .btn-primary { background: #059669; color: white; }
            .btn-secondary { background: #3b82f6; color: white; }
            .btn:hover { transform: translateY(-1px); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); }
            .footer { background: #f8fafc; padding: 20px; text-align: center; color: #64748b; font-size: 14px; }
            .urgent { background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 15px; margin: 20px 0; }
            .urgent-icon { color: #dc2626; font-size: 18px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🚨 New Lead Alert!</h1>
              <p>Someone is interested in your Al-Quran teaching services</p>
            </div>
            
            <div class="content">
              <div class="urgent">
                <span class="urgent-icon">⚡</span>
                <strong>Action Required:</strong> A new potential student has submitted an inquiry. Respond within 24 hours for best conversion rates!
              </div>
              
              <div class="lead-info">
                <h3 style="margin-top: 0; color: #059669;">📋 Lead Information</h3>
                
                <div class="info-row">
                  <span class="info-label">👤 Name:</span>
                  <span class="info-value"><strong>${lead.full_name}</strong></span>
                </div>
                
                <div class="info-row">
                  <span class="info-label">📧 Email:</span>
                  <span class="info-value"><a href="mailto:${lead.email}" style="color: #059669;">${lead.email}</a></span>
                </div>
                
                <div class="info-row">
                  <span class="info-label">📱 Phone:</span>
                  <span class="info-value"><a href="tel:${lead.phone}" style="color: #059669;">${lead.phone}</a></span>
                </div>
                
                <div class="info-row">
                  <span class="info-label">🎯 Service:</span>
                  <span class="info-value"><strong>${lead.service}</strong></span>
                </div>
                
                ${lead.student_age ? `
                <div class="info-row">
                  <span class="info-label">👶 Student Age:</span>
                  <span class="info-value">${lead.student_age}</span>
                </div>
                ` : ''}
                
                ${lead.preferred_time ? `
                <div class="info-row">
                  <span class="info-label">⏰ Preferred Time:</span>
                  <span class="info-value">${lead.preferred_time}</span>
                </div>
                ` : ''}
                
                <div class="info-row">
                  <span class="info-label">📅 Submitted:</span>
                  <span class="info-value">${submissionTime}</span>
                </div>
              </div>
              
              ${lead.message ? `
              <div class="message-box">
                <h4 style="margin-top: 0; color: #059669;">💬 Message from ${lead.full_name}:</h4>
                <p style="margin-bottom: 0; white-space: pre-wrap;">${lead.message}</p>
              </div>
              ` : ''}
              
              <div class="actions">
                <a href="mailto:${lead.email}?subject=Re: Al-Quran Classes Inquiry&body=Dear ${lead.full_name},%0D%0A%0D%0AThank you for your interest in our Al-Quran teaching services. I would be delighted to discuss how we can help you on your Quran learning journey.%0D%0A%0D%0ABest regards,%0D%0AUstazah Norazah" class="btn btn-primary">
                  📧 Reply via Email
                </a>
                <a href="tel:${lead.phone}" class="btn btn-secondary">
                  📞 Call Now
                </a>
              </div>
              
              <div style="background: #f0f9ff; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #0369a1;">💡 Quick Response Tips:</h4>
                <ul style="margin-bottom: 0; color: #0f172a;">
                  <li>Respond within 24 hours for best conversion rates</li>
                  <li>Personalize your response based on their service interest</li>
                  <li>Offer a free consultation or trial class</li>
                  <li>Ask about their current Quran learning level</li>
                </ul>
              </div>
            </div>
            
            <div class="footer">
              <p>This notification was sent from your Al-Quran teaching website lead capture system.</p>
              <p>🌐 <a href="http://localhost:3001/admin" style="color: #059669;">View in Admin Dashboard</a></p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
🚨 NEW LEAD ALERT! 🚨

Someone is interested in your Al-Quran teaching services!

LEAD INFORMATION:
👤 Name: ${lead.full_name}
📧 Email: ${lead.email}
📱 Phone: ${lead.phone}
🎯 Service: ${lead.service}
${lead.student_age ? `👶 Student Age: ${lead.student_age}\n` : ''}${lead.preferred_time ? `⏰ Preferred Time: ${lead.preferred_time}\n` : ''}📅 Submitted: ${submissionTime}

${lead.message ? `💬 MESSAGE:\n${lead.message}\n\n` : ''}

QUICK ACTIONS:
📧 Email: ${lead.email}
📞 Phone: ${lead.phone}

💡 TIP: Respond within 24 hours for best conversion rates!

View full details: http://localhost:3001/admin
    `
  };
};

// Function to send new lead notification email
export async function sendNewLeadNotification(lead: ContactLead) {
  try {
    const resend = getResendClient();

    if (!resend) {
      console.log('Email service not configured. Skipping email notification.');
      return { message: 'Email service not configured' };
    }

    const emailTemplate = createNewLeadEmailTemplate(lead);

    const { data, error } = await resend.emails.send({
      from: FROM_EMAIL,
      to: [TO_EMAIL],
      subject: emailTemplate.subject,
      html: emailTemplate.html,
      text: emailTemplate.text,
    });

    if (error) {
      console.error('Error sending email notification:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }

    console.log('Email notification sent successfully:', data);
    return data;
  } catch (error) {
    console.error('Error in sendNewLeadNotification:', error);
    throw error;
  }
}

// Function to send daily lead summary
export async function sendDailyLeadSummary(leads: ContactLead[]) {
  if (leads.length === 0) return;

  const resend = getResendClient();

  if (!resend) {
    console.log('Email service not configured. Skipping daily summary.');
    return { message: 'Email service not configured' };
  }

  const today = new Date().toLocaleDateString('en-SG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const emailTemplate = {
    subject: `📊 Daily Lead Summary - ${leads.length} new lead${leads.length > 1 ? 's' : ''} (${today})`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; }
            .header { background: linear-gradient(135deg, #059669, #10b981); color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .lead-item { border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin: 10px 0; }
            .lead-name { font-weight: 600; color: #059669; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>📊 Daily Lead Summary</h1>
              <p>${today} - ${leads.length} new lead${leads.length > 1 ? 's' : ''}</p>
            </div>
            <div class="content">
              ${leads.map(lead => `
                <div class="lead-item">
                  <div class="lead-name">${lead.full_name}</div>
                  <div>📧 ${lead.email} | 📱 ${lead.phone}</div>
                  <div>🎯 ${lead.service}</div>
                  <div>⏰ ${lead.created_at ? new Date(lead.created_at).toLocaleTimeString('en-SG') : 'Unknown time'}</div>
                </div>
              `).join('')}
              <p style="text-align: center; margin-top: 20px;">
                <a href="http://localhost:3001/admin" style="background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px;">View All Leads</a>
              </p>
            </div>
          </div>
        </body>
      </html>
    `
  };

  try {
    const { data, error } = await resend.emails.send({
      from: FROM_EMAIL,
      to: [TO_EMAIL],
      subject: emailTemplate.subject,
      html: emailTemplate.html,
    });

    if (error) {
      console.error('Error sending daily summary:', error);
      throw new Error(`Failed to send daily summary: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error in sendDailyLeadSummary:', error);
    throw error;
  }
}
