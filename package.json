{"name": "ustazah-norazah-quran", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next out node_modules/.cache"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.50.0", "@vercel/analytics": "^1.5.0", "critters": "^0.0.23", "lucide-react": "^0.522.0", "next": "15.3.4", "next-intl": "^4.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}