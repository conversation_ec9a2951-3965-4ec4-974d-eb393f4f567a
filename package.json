{"name": "ustazah-norazah-quran", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@next/font": "^14.2.15", "@supabase/supabase-js": "^2.50.0", "@types/react-google-recaptcha": "^2.1.9", "@vercel/analytics": "^1.5.0", "dompurify": "^3.2.6", "express-rate-limit": "^7.5.1", "express-slow-down": "^2.1.0", "framer-motion": "^12.18.1", "helmet": "^8.1.0", "lucide-react": "^0.522.0", "next": "15.3.4", "next-intl": "^4.3.4", "next-seo": "^6.8.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "resend": "^4.6.0", "validator": "^13.15.15", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}