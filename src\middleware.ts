import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Check if the pathname is exactly '/'
  if (request.nextUrl.pathname === '/') {
    // Redirect to /en
    return NextResponse.redirect(new URL('/en', request.url));
  }

  // For all other paths, continue normally
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Match all paths except static files and API routes
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
};
