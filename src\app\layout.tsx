import type { Metadata } from "next";
import "./globals.css";
import { Analytics } from '@vercel/analytics/react';
import { Toaster } from 'react-hot-toast';
import GoogleAnalytics from '@/components/GoogleAnalytics';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { LanguageProvider } from '@/contexts/LanguageContext';

export const metadata: Metadata = {
  title: {
    default: "<PERSON><PERSON><PERSON><PERSON> - Al-Quran Teacher in Singapore",
    template: "%s | <PERSON><PERSON><PERSON><PERSON> - Al-Quran Teacher"
  },
  description: "<PERSON>rn Al-Quran with <PERSON><PERSON><PERSON><PERSON>, an experienced female Quran teacher in Singapore. Offering personalized Quran classes, Tajweed, and Islamic studies for all ages.",
  keywords: [
    "Al-Quran teacher Singapore",
    "Female Quran teacher",
    "Ustazah Norazah",
    "Quran classes Singapore",
    "Tajweed lessons",
    "Islamic studies",
    "Online Quran classes",
    "Quran tuition"
  ],
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>" }],
  creator: "<PERSON><PERSON><PERSON><PERSON>",
  publisher: "Ustazah Norazah Quran Academy",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ustazah-norazah.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Ustazah <PERSON>zah - Al-Quran Teacher in Singapore",
    description: "Learn Al-Quran with Ustazah Norazah, an experienced female Quran teacher in Singapore. Offering personalized Quran classes, Tajweed, and Islamic studies for all ages.",
    url: 'https://ustazah-norazah.com',
    siteName: 'Ustazah Norazah Quran Academy',
    locale: 'en_SG',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Ustazah Norazah - Al-Quran Teacher in Singapore',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Ustazah Norazah - Al-Quran Teacher in Singapore",
    description: "Learn Al-Quran with Ustazah Norazah, an experienced female Quran teacher in Singapore.",
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'EducationalOrganization',
  name: 'Ustazah Norazah Quran Academy',
  description: 'Professional Al-Quran teaching services in Singapore',
  url: 'https://ustazah-norazah.com',
  logo: 'https://ustazah-norazah.com/logo.png',
  contactPoint: {
    '@type': 'ContactPoint',
    telephone: '+65 8835 2027',
    contactType: 'customer service',
    areaServed: 'SG',
    availableLanguage: ['English', 'Malay', 'Arabic']
  },
  address: {
    '@type': 'PostalAddress',
    addressLocality: 'Singapore',
    addressRegion: 'Singapore',
    addressCountry: 'SG'
  },
  founder: {
    '@type': 'Person',
    name: 'Ustazah Norazah',
    jobTitle: 'Al-Quran Teacher',
    description: 'Experienced female Al-Quran teacher specializing in Tajweed and Islamic studies'
  },
  offers: {
    '@type': 'Offer',
    category: 'Education',
    description: 'Al-Quran classes, Tajweed lessons, and Islamic studies'
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </head>
      <body className="antialiased bg-white dark:bg-slate-900 text-gray-900 dark:text-slate-100 transition-colors duration-300">
        <GoogleAnalytics />
        <ThemeProvider>
          <LanguageProvider>
            {children}
          </LanguageProvider>
        </ThemeProvider>
        <Toaster position="top-right" />
        <Analytics />
      </body>
    </html>
  );
}