'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { 
  Download, 
  BookOpen, 
  Clock, 
  Calendar, 
  Heart, 
  Star,
  FileText,
  Headphones,
  Video,
  Mail,
  Gift,
  CheckCircle
} from 'lucide-react';

const emailSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
});

type EmailFormData = z.infer<typeof emailSchema>;

const ResourcesPage = () => {
  const [downloadingResource, setDownloadingResource] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
  });

  const resources = [
    {
      id: 'quran-learning-guide',
      title: 'Complete Quran Learning Guide',
      description: 'A comprehensive 50-page guide covering basics of Quran reading, Tajweed rules, and learning strategies.',
      icon: BookOpen,
      type: 'PDF Guide',
      pages: '50 pages',
      downloads: '2,500+',
      featured: true,
    },
    {
      id: 'prayer-times-singapore',
      title: 'Singapore Prayer Times 2025',
      description: 'Annual prayer schedule for Singapore with Qibla direction and Islamic calendar dates.',
      icon: Clock,
      type: 'PDF Calendar',
      pages: '12 pages',
      downloads: '1,800+',
      featured: false,
    },
    {
      id: 'tajweed-rules-chart',
      title: 'Tajweed Rules Reference Chart',
      description: 'Visual guide to all major Tajweed rules with examples and pronunciation tips.',
      icon: Star,
      type: 'Infographic',
      pages: '4 pages',
      downloads: '3,200+',
      featured: true,
    },
    {
      id: 'islamic-calendar-2024',
      title: 'Islamic Calendar 2025',
      description: 'Complete Hijri calendar with important Islamic dates and Singapore public holidays.',
      icon: Calendar,
      type: 'PDF Calendar',
      pages: '24 pages',
      downloads: '1,500+',
      featured: false,
    },
    {
      id: 'children-quran-activities',
      title: 'Children\'s Quran Activities',
      description: 'Fun worksheets and activities to help children learn Arabic letters and basic Quran verses.',
      icon: Heart,
      type: 'Activity Pack',
      pages: '30 pages',
      downloads: '2,100+',
      featured: true,
    },
    {
      id: 'daily-duas-collection',
      title: 'Daily Duas Collection',
      description: 'Essential daily prayers with Arabic text, transliteration, and English translation.',
      icon: FileText,
      type: 'PDF Booklet',
      pages: '20 pages',
      downloads: '2,800+',
      featured: false,
    },
    {
      id: 'quran-audio-lessons',
      title: 'Basic Quran Audio Lessons',
      description: 'Audio recordings of basic Quran recitation lessons with proper pronunciation.',
      icon: Headphones,
      type: 'Audio Pack',
      pages: '10 lessons',
      downloads: '1,200+',
      featured: false,
    },
    {
      id: 'tajweed-video-series',
      title: 'Tajweed Video Tutorial Series',
      description: 'Step-by-step video lessons covering fundamental Tajweed rules and techniques.',
      icon: Video,
      type: 'Video Series',
      pages: '8 videos',
      downloads: '900+',
      featured: true,
    },
  ];

  const handleDownload = async (data: EmailFormData, resourceId: string) => {
    setDownloadingResource(resourceId);
    
    try {
      // Simulate API call to save email and trigger download
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('Download link sent to your email!');
      reset();
      
      // In a real implementation, this would trigger the actual download
      // For now, we'll just show a success message
      setTimeout(() => {
        toast.success('Resource downloaded successfully!');
      }, 1000);
      
    } catch (error) {
      toast.error('Failed to process download. Please try again.');
    } finally {
      setDownloadingResource(null);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-br from-blue-50 to-green-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Gift className="w-4 h-4" />
              <span>Free Islamic Learning Resources</span>
            </div>
            
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Enhance Your Quran Learning Journey
            </h1>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Access our collection of free Islamic educational materials, guides, and tools 
              to support your Quran learning and spiritual growth.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Download className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Instant Download</h3>
                <p className="text-gray-600 text-sm">Get immediate access to all resources</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Quality Content</h3>
                <p className="text-gray-600 text-sm">Carefully curated by Islamic scholars</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Completely Free</h3>
                <p className="text-gray-600 text-sm">No hidden costs or subscriptions</p>
              </div>
            </div>
          </div>
        </section>

        {/* Resources Grid */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Available Resources</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Simply enter your email to receive instant access to any of these valuable resources.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {resources.map((resource) => (
                <div
                  key={resource.id}
                  className={`bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${
                    resource.featured ? 'ring-2 ring-blue-200' : ''
                  }`}
                >
                  {resource.featured && (
                    <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium mb-4 inline-block">
                      Featured
                    </div>
                  )}

                  <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <resource.icon className="w-8 h-8 text-blue-600" />
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-3">{resource.title}</h3>
                  <p className="text-gray-600 mb-4">{resource.description}</p>

                  <div className="flex justify-between items-center text-sm text-gray-500 mb-6">
                    <span>{resource.type}</span>
                    <span>{resource.pages}</span>
                    <span>{resource.downloads} downloads</span>
                  </div>

                  {/* Download Form */}
                  <form
                    onSubmit={handleSubmit((data) => handleDownload(data, resource.id))}
                    className="space-y-4"
                  >
                    <div>
                      <input
                        {...register('name')}
                        type="text"
                        placeholder="Your name"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent text-sm"
                      />
                      {errors.name && (
                        <p className="mt-1 text-xs text-red-600">{errors.name.message}</p>
                      )}
                    </div>

                    <div>
                      <input
                        {...register('email')}
                        type="email"
                        placeholder="Your email address"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent text-sm"
                      />
                      {errors.email && (
                        <p className="mt-1 text-xs text-red-600">{errors.email.message}</p>
                      )}
                    </div>

                    <button
                      type="submit"
                      disabled={downloadingResource === resource.id}
                      className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2"
                    >
                      {downloadingResource === resource.id ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Processing...</span>
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4" />
                          <span>Download Free</span>
                        </>
                      )}
                    </button>
                  </form>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="py-16 bg-blue-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="w-8 h-8 text-blue-600" />
            </div>
            
            <h2 className="text-3xl font-bold text-white mb-4">
              Get New Resources First
            </h2>
            
            <p className="text-xl text-blue-100 mb-8">
              Subscribe to receive new Islamic learning materials, Quran tips, and exclusive content directly in your inbox.
            </p>

            <div className="max-w-md mx-auto flex gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg focus:ring-2 focus:ring-white focus:outline-none"
              />
              <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                Subscribe
              </button>
            </div>

            <p className="text-sm text-blue-200 mt-4">
              No spam, unsubscribe anytime. We respect your privacy.
            </p>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default ResourcesPage;
