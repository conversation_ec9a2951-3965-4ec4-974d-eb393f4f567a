import { NextRequest, NextResponse } from 'next/server';

// Simple test endpoint to verify the hadith API is working
export async function GET(request: NextRequest) {
  try {
    // Test the main hadith endpoint
    const baseUrl = request.nextUrl.origin;
    const response = await fetch(`${baseUrl}/api/hadith`);
    
    if (!response.ok) {
      throw new Error(`Hadith API returned ${response.status}`);
    }
    
    const data = await response.json();
    
    return NextResponse.json({
      success: true,
      message: 'Hadith API is working correctly',
      testResult: {
        apiStatus: response.status,
        dataReceived: !!data.data,
        hadithText: data.data?.text?.substring(0, 50) + '...',
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('Hadith API test failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Hadith API test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, {
      status: 500
    });
  }
}
