'use client';

import Link from 'next/link';
import { BookOpen, Download, Play, FileText, ArrowLeft, ExternalLink, Clock, Users, Star } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const resources = [
  {
    id: 'quran-basics',
    title: 'Quran Reading Basics',
    description: 'Essential guide for beginners learning to read the Quran with proper pronunciation.',
    type: 'PDF Guide',
    icon: BookOpen,
    downloadUrl: '#',
    size: '2.5 MB',
    pages: 24,
    difficulty: 'Beginner',
    category: 'Reading'
  },
  {
    id: 'tajweed-rules',
    title: 'Tajweed Rules Reference',
    description: 'Comprehensive reference chart for all major Tajweed rules with examples.',
    type: 'PDF Chart',
    icon: FileText,
    downloadUrl: '#',
    size: '1.8 MB',
    pages: 12,
    difficulty: 'Intermediate',
    category: 'Tajweed'
  },
  {
    id: 'arabic-alphabet',
    title: 'Arabic Alphabet Practice',
    description: 'Interactive worksheets for learning and practicing Arabic letters.',
    type: 'Worksheet',
    icon: FileText,
    downloadUrl: '#',
    size: '3.2 MB',
    pages: 36,
    difficulty: 'Beginner',
    category: 'Writing'
  },
  {
    id: 'daily-duas',
    title: 'Daily Duas Collection',
    description: 'Essential daily prayers and supplications with Arabic text and translations.',
    type: 'PDF Book',
    icon: BookOpen,
    downloadUrl: '#',
    size: '4.1 MB',
    pages: 48,
    difficulty: 'All Levels',
    category: 'Duas'
  },
  {
    id: 'memorization-tips',
    title: 'Quran Memorization Guide',
    description: 'Proven techniques and strategies for effective Quran memorization.',
    type: 'PDF Guide',
    icon: BookOpen,
    downloadUrl: '#',
    size: '2.9 MB',
    pages: 32,
    difficulty: 'All Levels',
    category: 'Memorization'
  },
  {
    id: 'children-activities',
    title: 'Islamic Activities for Children',
    description: 'Fun and educational activities to teach Islamic values to young learners.',
    type: 'Activity Pack',
    icon: FileText,
    downloadUrl: '#',
    size: '5.3 MB',
    pages: 60,
    difficulty: 'Children',
    category: 'Activities'
  }
];

const categories = ['All', 'Reading', 'Tajweed', 'Writing', 'Duas', 'Memorization', 'Activities'];

export default function ResourcesPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-slate-900 transition-colors duration-300">
      <Header />
      
      {/* Header */}
      <div className="bg-gradient-to-br from-emerald-50 via-white to-emerald-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-24 pt-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Learning Resources
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Free educational materials to support your Quran learning journey
            </p>
          </div>
        </div>
      </div>

      {/* Resources Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className="px-6 py-2 rounded-full bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-emerald-100 dark:hover:bg-emerald-900/30 hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors duration-300 font-medium"
            >
              {category}
            </button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {resources.map((resource) => (
            <div
              key={resource.id}
              className="bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group border border-slate-200 dark:border-slate-700"
            >
              {/* Header */}
              <div className="p-6 pb-4">
                <div className="flex items-start justify-between mb-4">
                  <div className="w-12 h-12 bg-emerald-100 dark:bg-emerald-900/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <resource.icon className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <span className="px-3 py-1 text-xs font-semibold text-emerald-600 dark:text-emerald-400 bg-emerald-100 dark:bg-emerald-900/30 rounded-full">
                    {resource.category}
                  </span>
                </div>

                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors duration-300">
                  {resource.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                  {resource.description}
                </p>

                {/* Resource Details */}
                <div className="flex flex-wrap gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <div className="flex items-center">
                    <FileText className="w-4 h-4 mr-1" />
                    {resource.pages} pages
                  </div>
                  <div className="flex items-center">
                    <Download className="w-4 h-4 mr-1" />
                    {resource.size}
                  </div>
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-1" />
                    {resource.difficulty}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {resource.type}
                  </span>
                  
                  <button className="inline-flex items-center text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300 font-medium transition-colors duration-300">
                    <Download className="w-4 h-4 mr-1" />
                    Download
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Resources Section */}
        <div className="mt-20">
          <div className="bg-gradient-to-r from-emerald-50 to-green-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl p-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Need More Resources?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Join our classes to access exclusive materials and personalized learning resources tailored to your needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/#contact"
                className="inline-flex items-center space-x-2 bg-emerald-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-colors duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
              >
                <span>Schedule a Class</span>
                <ExternalLink className="w-4 h-4" />
              </Link>
              <Link
                href="/blog"
                className="inline-flex items-center space-x-2 bg-white dark:bg-slate-800 text-emerald-600 dark:text-emerald-400 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors duration-200 border border-emerald-200 dark:border-emerald-800"
              >
                <BookOpen className="w-4 h-4" />
                <span>Read Our Blog</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Back to Home */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="text-center">
          <Link
            href="/"
            className="inline-flex items-center text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300 font-medium transition-colors duration-300"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back to Home
          </Link>
        </div>
      </div>

      <Footer />
    </div>
  );
}
