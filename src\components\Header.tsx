'use client';

import { useState } from 'react';
import Link from 'next/link';
import { <PERSON>u, X, BookOpen, Phone } from 'lucide-react';
import { useTranslations } from 'next-intl';
import DarkModeToggle from './DarkModeToggle';
import LanguageSwitcher from './LanguageSwitcher';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const t = useTranslations('navigation');

  const navigation = [
    { name: t('home'), href: '/' },
    { name: t('about'), href: '/#about' },
    { name: t('services'), href: '/#services' },
    { name: t('blog'), href: '/blog' },
    { name: t('contact'), href: '/#contact' },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 dark:bg-slate-900/95 backdrop-blur-md border-b border-slate-200/50 dark:border-slate-700/50 shadow-lg transition-all duration-300 animate-fade-in-down">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-24">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-4 group animate-fade-in-left">
            <div className="w-14 h-14 bg-gradient-to-br from-emerald-600 via-emerald-700 to-green-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-all duration-300 relative overflow-hidden border border-white/20">
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
              <div className="absolute inset-0 bg-gradient-to-tl from-emerald-400/20 to-transparent"></div>
              <BookOpen className="w-8 h-8 text-white relative z-10 drop-shadow-sm" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100 group-hover:text-emerald-600 transition-colors tracking-wide">Ustazah Norazah</h1>
              <p className="text-sm text-slate-600 dark:text-slate-400 font-medium tracking-wider">Al-Quran Teacher</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 animate-fade-in-right">
            {navigation.map((item, index) => (
              <Link
                key={item.name}
                href={item.href}
                className={`text-slate-700 dark:text-slate-300 hover:text-emerald-600 font-semibold text-base tracking-wide transition-colors duration-300 relative group animate-fade-in-down animate-stagger-${index + 1} ${
                  item.name === t('nav.contact') ? 'mr-4' : ''
                }`}
              >
                {item.name}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-emerald-600 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            ))}
          </nav>

          {/* CTA Button, Language Switcher & Dark Mode Toggle */}
          <div className="flex items-center space-x-4 animate-fade-in-right">
            <div className="hidden md:flex items-center space-x-6">
              <Link
                href="tel:+6588352027"
                className="flex items-center space-x-2 text-slate-700 dark:text-slate-300 hover:text-emerald-600 transition-colors duration-300 font-medium tracking-wide whitespace-nowrap"
              >
                <Phone className="w-4 h-4" />
                <span className="text-sm">{t('common.callNow')}</span>
              </Link>
              <Link
                href="/#contact"
                className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white px-4 py-2 rounded-lg font-medium text-sm tracking-wide transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 whitespace-nowrap"
              >
                Book Class
              </Link>
            </div>

            {/* Language Switcher */}
            <LanguageSwitcher />

            {/* Dark Mode Toggle */}
            <DarkModeToggle />
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-3 rounded-xl text-slate-700 dark:text-slate-300 hover:text-emerald-600 hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-300"
          >
            {isMenuOpen ? <X className="w-7 h-7" /> : <Menu className="w-7 h-7" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden py-6 border-t border-slate-200 dark:border-slate-700 animate-fade-in bg-white dark:bg-slate-900">
            <nav className="flex flex-col space-y-6">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-slate-700 dark:text-slate-300 hover:text-emerald-600 font-semibold text-lg transition-colors duration-300 px-4 py-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <div className="pt-6 border-t border-slate-200 dark:border-slate-700 space-y-4 px-4">
                <Link
                  href="/#contact"
                  className="block bg-gradient-to-r from-emerald-600 to-emerald-700 text-white px-8 py-4 rounded-2xl font-semibold text-center hover:from-emerald-700 hover:to-emerald-800 transition-all duration-300 shadow-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {t('common.bookClass')}
                </Link>
                <Link
                  href="tel:+6588352027"
                  className="flex items-center justify-center space-x-3 text-slate-700 dark:text-slate-300 hover:text-emerald-600 transition-colors duration-300 px-4 py-3 font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Phone className="w-5 h-5" />
                  <span>{t('common.callNow')}</span>
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
