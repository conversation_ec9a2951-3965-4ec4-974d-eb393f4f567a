'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'ms';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Simple translations object
const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.about': 'About',
    'nav.services': 'Services',
    'nav.blog': 'Blog',
    'nav.contact': 'Contact',
    'nav.admin': 'Admin',
    
    // Hero
    'hero.title': 'Learn Al-Quran with',
    'hero.subtitle': '<PERSON><PERSON><PERSON><PERSON>',
    'hero.description': 'Professional female Quran teacher in Singapore offering personalized Islamic education for women and children. Learn Quran recitation, Tajweed, and Islamic studies in a comfortable, supportive environment.',
    'hero.cta': 'Start Learning Today',
    'hero.experience': '15+ Years Experience',
    'hero.students': '500+ Happy Students',
    'hero.certified': 'Certified Teacher',
    
    // Common
    'common.bookClass': 'Book a Class',
    'common.callNow': 'Call Now',
    'common.readMore': 'Read More',
    'common.backToHome': '← Back to Home',
    'common.backToBlog': '← Back to Blog',
  },
  ms: {
    // Navigation
    'nav.home': 'Utama',
    'nav.about': 'Tentang',
    'nav.services': 'Perkhidmatan',
    'nav.blog': 'Blog',
    'nav.contact': 'Hubungi',
    'nav.admin': 'Admin',
    
    // Hero
    'hero.title': 'Belajar Al-Quran bersama',
    'hero.subtitle': 'Ustazah Norazah',
    'hero.description': 'Guru Al-Quran wanita profesional di Singapura yang menawarkan pendidikan Islam yang dipersonalisasi untuk wanita dan kanak-kanak. Belajar bacaan Al-Quran, Tajwid, dan pengajian Islam dalam persekitaran yang selesa dan menyokong.',
    'hero.cta': 'Mula Belajar Hari Ini',
    'hero.experience': '15+ Tahun Pengalaman',
    'hero.students': '500+ Pelajar Berpuas Hati',
    'hero.certified': 'Guru Bertauliah',
    
    // Common
    'common.bookClass': 'Tempah Kelas',
    'common.callNow': 'Hubungi Sekarang',
    'common.readMore': 'Baca Lagi',
    'common.backToHome': '← Kembali ke Utama',
    'common.backToBlog': '← Kembali ke Blog',
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('en');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Load saved language from localStorage only after mounting
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ms')) {
      setLanguage(savedLanguage);
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    if (mounted) {
      localStorage.setItem('language', lang);
    }
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}