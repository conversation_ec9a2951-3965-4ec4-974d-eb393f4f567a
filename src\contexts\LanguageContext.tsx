'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'ms';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Simple translations object
const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.about': 'About',
    'nav.services': 'Services',
    'nav.blog': 'Blog',
    'nav.contact': 'Contact',
    'nav.admin': 'Admin',
    
    // Hero
    'hero.title': 'Learn Al-Quran with',
    'hero.subtitle': '<PERSON><PERSON><PERSON><PERSON>',
    'hero.description': 'Professional female Quran teacher in Singapore offering personalized Islamic education for women and children. Learn Quran recitation, Tajweed, and Islamic studies in a comfortable, supportive environment.',
    'hero.cta': 'Start Learning Today',
    'hero.experience': '15+ Years Experience',
    'hero.students': '500+ Happy Students',
    'hero.certified': 'Certified Teacher',
    
    // Common
    'common.bookClass': 'Book a Class',
    'common.callNow': 'Call Now',
    'common.readMore': 'Read More',
    'common.backToHome': '← Back to Home',
    'common.backToBlog': '← Back to Blog',
    'common.loading': 'Loading...',
    'common.learnMore': 'Learn More',
    'common.getStarted': 'Get Started',

    // Services
    'services.title': 'Our Services',
    'services.subtitle': 'Comprehensive Al-Quran Learning Programs',
    'services.description': 'We offer a range of specialized programs designed to help you master the beautiful art of Quranic recitation and understanding.',
    'services.individual.title': 'Individual Classes',
    'services.individual.description': 'One-on-one personalized Quran lessons tailored to your learning pace and goals.',
    'services.group.title': 'Group Classes',
    'services.group.description': 'Learn with fellow students in small groups for a collaborative learning experience.',
    'services.online.title': 'Online Classes',
    'services.online.description': 'Convenient online Quran lessons from the comfort of your home via video call.',
    'services.tajweed.title': 'Tajweed Classes',
    'services.tajweed.description': 'Master the proper pronunciation and rules of Quranic recitation with our comprehensive Tajweed program.',
    'services.memorization.title': 'Quran Memorization',
    'services.memorization.description': 'Structured Hifz program to help you memorize the Holy Quran with proper technique and retention methods.',
    'services.children.title': 'Children\'s Program',
    'services.children.description': 'Fun and engaging Quran classes designed specifically for young learners.',
    'services.intensive.title': 'Intensive Courses',
    'services.intensive.description': 'Accelerated learning programs for dedicated students who want to progress quickly.',

    // About
    'about.title': 'About Ustazah Norazah',
    'about.subtitle': 'Dedicated Islamic Educator',
    'about.description': 'With over 15 years of experience in Islamic education, Ustazah Norazah is committed to providing quality Al-Quran education.',
    'about.greeting': 'Assalamu Alaikum! I am Ustazah Norazah, a dedicated Quran teacher based in Punggol, Singapore. With over 15 years of experience in Islamic education, I have had the privilege of guiding hundreds of students on their journey to connect with the Holy Quran.',
    'about.philosophy': 'My teaching philosophy centers on creating a nurturing environment where students of all ages can learn at their own pace while developing a deep love and understanding of the Quran. I believe that every student is unique, and I tailor my approach to meet their individual needs.',
    'about.passion': 'My passion for the Qur\'an has inspired me to dedicate myself fully to improving my recitation and understanding, while also sharing the love for the Qur\'an by guiding and inspiring others on their own journeys.',
    'about.experience': 'Years of Teaching Experience',
    'about.students': 'Students Taught',
    'about.programs': 'Programs Offered',

    // Testimonials
    'testimonials.title': 'What Our Students Say',
    'testimonials.subtitle': 'Testimonials from Our Learning Community',

    // Contact
    'contact.title': 'Get In Touch',
    'contact.subtitle': 'Ready to Start Your Quranic Journey?',
    'contact.description': 'Contact us today to schedule your first lesson or to learn more about our programs.',
    'contact.name': 'Full Name',
    'contact.email': 'Email Address',
    'contact.phone': 'Phone Number',
    'contact.message': 'Your Message',
    'contact.send': 'Send Message',
    'contact.info': 'Contact Information',
    'contact.address': 'Address',
    'contact.hours': 'Teaching Hours',

    // Hadith
    'hadith.title': 'Hadith of the Week',

    // Footer
    'footer.description': 'Professional female Quran teacher in Singapore offering personalized Islamic education for women and children.',
    'footer.quickLinks': 'Quick Links',
    'footer.services': 'Services',
    'footer.contact': 'Contact Info',
    'footer.followUs': 'Follow Us',
    'footer.rights': 'All rights reserved.',
    'footer.location': 'Location',
    'footer.phone': 'Phone',
    'footer.email': 'Email',

    // Availability
    'availability.title': 'Class Schedule',
    'availability.subtitle': 'Available Time Slots',
    'availability.description': 'Choose from our available time slots for your Al-Quran learning sessions.',
    'availability.available': 'Available',
    'availability.booked': 'Booked',
    'availability.morning': 'Morning',
    'availability.afternoon': 'Afternoon',
    'availability.evening': 'Evening',
    'availability.pricing_title': 'Pricing',
    'availability.price_hourly': '$30',
    'availability.price_half_hour': '$15',
    'availability.book_now': 'Book Now',
  },
  ms: {
    // Navigation
    'nav.home': 'Utama',
    'nav.about': 'Tentang',
    'nav.services': 'Perkhidmatan',
    'nav.blog': 'Blog',
    'nav.contact': 'Hubungi',
    'nav.admin': 'Admin',
    
    // Hero
    'hero.title': 'Belajar Al-Quran bersama',
    'hero.subtitle': 'Ustazah Norazah',
    'hero.description': 'Guru Al-Quran wanita profesional di Singapura yang menawarkan pendidikan Islam yang dipersonalisasi untuk wanita dan kanak-kanak. Belajar bacaan Al-Quran, Tajwid, dan pengajian Islam dalam persekitaran yang selesa dan menyokong.',
    'hero.cta': 'Mula Belajar Hari Ini',
    'hero.experience': '15+ Tahun Pengalaman',
    'hero.students': '500+ Pelajar Berpuas Hati',
    'hero.certified': 'Guru Bertauliah',
    
    // Common
    'common.bookClass': 'Tempah Kelas',
    'common.callNow': 'Hubungi Sekarang',
    'common.readMore': 'Baca Lagi',
    'common.backToHome': '← Kembali ke Utama',
    'common.backToBlog': '← Kembali ke Blog',
    'common.loading': 'Memuatkan...',
    'common.learnMore': 'Ketahui Lebih Lanjut',
    'common.getStarted': 'Mulakan',

    // Services
    'services.title': 'Perkhidmatan Kami',
    'services.subtitle': 'Program Pembelajaran Al-Quran yang Komprehensif',
    'services.description': 'Kami menawarkan pelbagai program khusus yang direka untuk membantu anda menguasai seni bacaan dan pemahaman Al-Quran yang indah.',
    'services.individual.title': 'Kelas Individu',
    'services.individual.description': 'Pelajaran Al-Quran peribadi satu-dengan-satu yang disesuaikan dengan kadar dan matlamat pembelajaran anda.',
    'services.group.title': 'Kelas Berkumpulan',
    'services.group.description': 'Belajar bersama pelajar lain dalam kumpulan kecil untuk pengalaman pembelajaran kolaboratif.',
    'services.online.title': 'Kelas Dalam Talian',
    'services.online.description': 'Pelajaran Al-Quran dalam talian yang mudah dari keselesaan rumah anda melalui panggilan video.',
    'services.tajweed.title': 'Kelas Tajwid',
    'services.tajweed.description': 'Kuasai sebutan yang betul dan hukum bacaan Al-Quran dengan program Tajwid komprehensif kami.',
    'services.memorization.title': 'Hafazan Al-Quran',
    'services.memorization.description': 'Program Hifz berstruktur untuk membantu anda menghafaz Al-Quran dengan teknik dan kaedah ingatan yang betul.',
    'services.children.title': 'Program Kanak-kanak',
    'services.children.description': 'Kelas Al-Quran yang menyeronokkan dan menarik yang direka khusus untuk pelajar muda.',
    'services.intensive.title': 'Kursus Intensif',
    'services.intensive.description': 'Program pembelajaran dipercepatkan untuk pelajar berdedikasi yang ingin maju dengan cepat.',

    // About
    'about.title': 'Tentang Ustazah Norazah',
    'about.subtitle': 'Pendidik Islam yang Berdedikasi',
    'about.description': 'Dengan pengalaman lebih 15 tahun dalam pendidikan Islam, Ustazah Norazah komited menyediakan pendidikan Al-Quran berkualiti.',
    'about.greeting': 'Assalamu Alaikum! Saya Ustazah Norazah, seorang guru Al-Quran yang berdedikasi di Punggol, Singapura. Dengan pengalaman lebih 15 tahun dalam pendidikan Islam, saya telah mendapat keistimewaan membimbing ratusan pelajar dalam perjalanan mereka untuk berhubung dengan Al-Quran.',
    'about.philosophy': 'Falsafah pengajaran saya berpusat pada mewujudkan persekitaran yang memupuk di mana pelajar dari semua peringkat umur boleh belajar mengikut kadar mereka sendiri sambil mengembangkan kasih sayang dan pemahaman yang mendalam terhadap Al-Quran. Saya percaya bahawa setiap pelajar adalah unik, dan saya menyesuaikan pendekatan saya untuk memenuhi keperluan individu mereka.',
    'about.passion': 'Keghairahan saya terhadap Al-Quran telah menginspirasi saya untuk mendedikasikan diri sepenuhnya untuk meningkatkan bacaan dan pemahaman saya, sambil juga berkongsi kasih sayang terhadap Al-Quran dengan membimbing dan menginspirasi orang lain dalam perjalanan mereka sendiri.',
    'about.experience': 'Tahun Pengalaman Mengajar',
    'about.students': 'Pelajar Diajar',
    'about.programs': 'Program Ditawarkan',

    // Testimonials
    'testimonials.title': 'Kata Pelajar Kami',
    'testimonials.subtitle': 'Testimoni dari Komuniti Pembelajaran Kami',

    // Contact
    'contact.title': 'Hubungi Kami',
    'contact.subtitle': 'Bersedia Memulakan Perjalanan Al-Quran Anda?',
    'contact.description': 'Hubungi kami hari ini untuk menjadualkan pelajaran pertama anda atau untuk mengetahui lebih lanjut tentang program kami.',
    'contact.name': 'Nama Penuh',
    'contact.email': 'Alamat E-mel',
    'contact.phone': 'Nombor Telefon',
    'contact.message': 'Mesej Anda',
    'contact.send': 'Hantar Mesej',
    'contact.info': 'Maklumat Hubungan',
    'contact.address': 'Alamat',
    'contact.hours': 'Waktu Mengajar',

    // Hadith
    'hadith.title': 'Hadis Minggu Ini',

    // Footer
    'footer.description': 'Guru Al-Quran wanita profesional di Singapura yang menawarkan pendidikan Islam yang dipersonalisasi untuk wanita dan kanak-kanak.',
    'footer.quickLinks': 'Pautan Pantas',
    'footer.services': 'Perkhidmatan',
    'footer.contact': 'Maklumat Hubungan',
    'footer.followUs': 'Ikuti Kami',
    'footer.rights': 'Hak cipta terpelihara.',
    'footer.location': 'Lokasi',
    'footer.phone': 'Telefon',
    'footer.email': 'E-mel',

    // Availability
    'availability.title': 'Jadual Kelas',
    'availability.subtitle': 'Slot Masa Tersedia',
    'availability.description': 'Pilih dari slot masa tersedia untuk sesi pembelajaran Al-Quran anda.',
    'availability.available': 'Tersedia',
    'availability.booked': 'Ditempah',
    'availability.morning': 'Pagi',
    'availability.afternoon': 'Petang',
    'availability.evening': 'Malam',
    'availability.pricing_title': 'Harga',
    'availability.price_hourly': '$30',
    'availability.price_half_hour': '$15',
    'availability.book_now': 'Tempah Sekarang',
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('en');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const updateLanguageFromURL = () => {
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        const urlLanguage = currentPath.split('/')[1] as Language;
        if (urlLanguage === 'en' || urlLanguage === 'ms') {
          setLanguage(urlLanguage);
          localStorage.setItem('language', urlLanguage);
          return;
        }
      }

      // Fallback to saved language from localStorage
      if (typeof window !== 'undefined') {
        const savedLanguage = localStorage.getItem('language') as Language;
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ms')) {
          setLanguage(savedLanguage);
        }
      }
    };

    // Initial language detection
    updateLanguageFromURL();

    // Listen for URL changes (for SPA navigation)
    const handlePopState = () => {
      updateLanguageFromURL();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', handlePopState);

      // Also listen for pushstate/replacestate (for programmatic navigation)
      const originalPushState = window.history.pushState;
      const originalReplaceState = window.history.replaceState;

      window.history.pushState = function(...args) {
        originalPushState.apply(window.history, args);
        setTimeout(updateLanguageFromURL, 0);
      };

      window.history.replaceState = function(...args) {
        originalReplaceState.apply(window.history, args);
        setTimeout(updateLanguageFromURL, 0);
      };

      return () => {
        window.removeEventListener('popstate', handlePopState);
        window.history.pushState = originalPushState;
        window.history.replaceState = originalReplaceState;
      };
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    if (mounted) {
      localStorage.setItem('language', lang);
    }
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}