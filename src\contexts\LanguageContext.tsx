'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'ms';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Simple translations object
const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.about': 'About',
    'nav.services': 'Services',
    'nav.blog': 'Blog',
    'nav.contact': 'Contact',
    'nav.admin': 'Admin',
    
    // Hero
    'hero.title': 'Learn Al-Quran with',
    'hero.subtitle': '<PERSON><PERSON><PERSON><PERSON>',
    'hero.description': 'Professional female Quran teacher in Singapore offering personalized Islamic education for women and children. Learn Quran recitation, Tajweed, and Islamic studies in a comfortable, supportive environment.',
    'hero.cta': 'Start Learning Today',
    'hero.experience': '15+ Years Experience',
    'hero.students': '500+ Happy Students',
    'hero.certified': 'Certified Teacher',
    
    // Common
    'common.bookClass': 'Book a Class',
    'common.callNow': 'Call Now',
    'common.readMore': 'Read More',
    'common.backToHome': '← Back to Home',
    'common.backToBlog': '← Back to Blog',
    'common.loading': 'Loading...',
    'common.learnMore': 'Learn More',
    'common.getStarted': 'Get Started',
    'common.error': 'Unable to load content. Please try again later.',

    // Services
    'services.title': 'Our Services',
    'services.subtitle': 'Comprehensive Al-Quran Learning Programs',
    'services.description': 'We offer a range of specialized programs designed to help you master the beautiful art of Quranic recitation and understanding.',
    'services.individual.title': 'Individual Classes',
    'services.individual.description': 'One-on-one personalized Quran lessons tailored to your learning pace and goals.',
    'services.group.title': 'Group Classes',
    'services.group.description': 'Learn with fellow students in small groups for a collaborative learning experience.',
    'services.online.title': 'Online Classes',
    'services.online.description': 'Convenient online Quran lessons from the comfort of your home via video call.',
    'services.tajweed.title': 'Tajweed Classes',
    'services.tajweed.description': 'Master the proper pronunciation and rules of Quranic recitation with our comprehensive Tajweed program.',
    'services.memorization.title': 'Quran Memorization',
    'services.memorization.description': 'Structured Hifz program to help you memorize the Holy Quran with proper technique and retention methods.',
    'services.children.title': 'Children\'s Program',
    'services.children.description': 'Fun and engaging Quran classes designed specifically for young learners.',
    'services.intensive.title': 'Intensive Courses',
    'services.intensive.description': 'Accelerated learning programs for dedicated students who want to progress quickly.',

    // Service Features
    'services.individual.feature1': 'Customized learning plan',
    'services.individual.feature2': 'Flexible scheduling',
    'services.individual.feature3': 'Personal attention',
    'services.individual.feature4': 'Progress tracking',
    'services.group.feature1': 'Small class sizes (3-5 students)',
    'services.group.feature2': 'Interactive learning',
    'services.group.feature3': 'Peer motivation',
    'services.group.feature4': 'Cost-effective',
    'services.online.feature1': 'Learn from anywhere',
    'services.online.feature2': 'Digital resources',
    'services.online.feature3': 'Recorded sessions',
    'services.online.feature4': 'Technical support',
    'services.tajweed.feature1': 'Detailed pronunciation guide',
    'services.tajweed.feature2': 'Audio practice sessions',
    'services.tajweed.feature3': 'Correction techniques',
    'services.tajweed.feature4': 'Certification available',
    'services.children.feature1': 'Age-appropriate methods',
    'services.children.feature2': 'Interactive activities',
    'services.children.feature3': 'Reward system',
    'services.children.feature4': 'Parent progress reports',
    'services.intensive.feature1': 'Fast-track learning',
    'services.intensive.feature2': 'Daily sessions available',
    'services.intensive.feature3': 'Goal-oriented approach',
    'services.intensive.feature4': 'Comprehensive materials',

    // Service Pricing & Duration
    'services.individual.price': 'From $80/session',
    'services.individual.duration': '60 minutes',
    'services.group.price': 'From $45/session',
    'services.group.duration': '90 minutes',
    'services.online.price': 'From $60/session',
    'services.online.duration': '60 minutes',
    'services.tajweed.price': 'From $90/session',
    'services.tajweed.duration': '75 minutes',
    'services.children.price': 'From $50/session',
    'services.children.duration': '45 minutes',
    'services.intensive.price': 'Custom pricing',
    'services.intensive.duration': 'Flexible',

    // Service Labels
    'services.mostPopular': 'Most Popular',
    'services.bookNow': 'Book Now',
    'services.notSure': 'Not sure which program is right for you?',
    'services.consultation': 'Schedule a free consultation to discuss your learning goals and find the perfect program. Or explore our educational blog for learning tips and guidance.',
    'services.freeConsultation': 'Free Consultation',
    'services.learningResources': 'Learning Resources',

    // About
    'about.title': 'About Ustazah Norazah',
    'about.subtitle': 'Dedicated Islamic Educator',
    'about.description': 'With over 15 years of experience in Islamic education, Ustazah Norazah is committed to providing quality Al-Quran education.',
    'about.greeting': 'Assalamu Alaikum! I am Ustazah Norazah, a dedicated Quran teacher based in Punggol, Singapore. With over 15 years of experience in Islamic education, I have had the privilege of guiding hundreds of students on their journey to connect with the Holy Quran.',
    'about.philosophy': 'My teaching philosophy centers on creating a nurturing environment where students of all ages can learn at their own pace while developing a deep love and understanding of the Quran. I believe that every student is unique, and I tailor my approach to meet their individual needs.',
    'about.passion': 'My passion for the Qur\'an has inspired me to dedicate myself fully to improving my recitation and understanding, while also sharing the love for the Qur\'an by guiding and inspiring others on their own journeys.',
    'about.experience': 'Years of Teaching Experience',
    'about.students': 'Students Taught',
    'about.programs': 'Programs Offered',

    // About Qualifications
    'about.qualification1': 'Certified Quran Teacher (Ijazah)',
    'about.qualification2': 'Bachelor\'s in Islamic Studies',
    'about.qualification3': 'Tajweed Specialist Certification',
    'about.qualification4': '15+ Years Teaching Experience',
    'about.qualification5': 'Fluent in English, Malay & Arabic',

    // About Achievements
    'about.studentsTaught': 'Students Taught',
    'about.quranCompletions': 'Quran Completions',
    'about.yearsExperience': 'Years Experience',
    'about.studentRating': 'Student Rating',

    // About Teaching Approach
    'about.teachingApproach': 'My Teaching Approach',
    'about.approachDescription': 'I believe in combining traditional Islamic teaching methods with modern educational approaches to create an effective and engaging learning experience.',
    'about.personalizedTitle': 'Personalized Learning',
    'about.personalizedDesc': 'Every student receives a customized learning plan based on their current level, goals, and learning style.',
    'about.patientTitle': 'Patient & Supportive',
    'about.patientDesc': 'Creating a comfortable environment where students feel confident to learn and make mistakes.',
    'about.holisticTitle': 'Holistic Education',
    'about.holisticDesc': 'Beyond recitation, we focus on understanding, spirituality, and practical application in daily life.',
    'about.modernTitle': 'Modern Methods',
    'about.modernDesc': 'Combining traditional Islamic teaching with modern educational techniques and technology.',
    'about.certifiedTeacher': 'Certified Teacher',
    'about.quote': '"Teaching the Quran is not just my profession, it\'s my passion and calling."',

    // Testimonials
    'testimonials.title': 'What Our Students Say',
    'testimonials.subtitle': 'Testimonials from Our Learning Community',

    // Contact
    'contact.title': 'Get In Touch',
    'contact.subtitle': 'Ready to Start Your Quranic Journey?',
    'contact.description': 'Contact us today to schedule your first lesson or to learn more about our programs.',
    'contact.name': 'Full Name',
    'contact.email': 'Email Address',
    'contact.phone': 'Phone Number',
    'contact.message': 'Your Message',
    'contact.send': 'Send Message',
    'contact.info': 'Contact Information',
    'contact.address': 'Address',
    'contact.hours': 'Teaching Hours',
    'contact.sendMessage': 'Send a Message',
    'contact.respond': 'We\'ll respond within 24 hours',
    'contact.location': 'Location',
    'contact.locationDetails1': 'Punggol Central, Singapore',
    'contact.locationDetails2': 'Home visits available',
    'contact.phoneDetails1': '+65 8835 2027',
    'contact.phoneDetails2': 'WhatsApp available',
    'contact.emailDetails1': '<EMAIL>',
    'contact.emailDetails2': 'Response within 24 hours',
    'contact.hoursDetails1': 'Mon-Fri: 2PM - 8PM',
    'contact.hoursDetails2': 'Sat-Sun: 9AM - 6PM',
    'contact.service': 'Service Interested',
    'contact.selectService': 'Select a service',
    'contact.studentAge': 'Student Age (Optional)',
    'contact.preferredTime': 'Preferred Time (Optional)',
    'contact.messagePlaceholder': 'Tell us about your learning goals, experience level, and any specific requirements...',
    'contact.submitting': 'Sending...',
    'contact.success': 'Message sent successfully!',
    'contact.error': 'Failed to send message. Please try again.',

    // Hadith
    'hadith.title': 'Hadith of the Week',

    // Footer
    'footer.description': 'Professional female Quran teacher in Singapore offering personalized Islamic education for women and children.',
    'footer.quickLinks': 'Quick Links',
    'footer.services': 'Services',
    'footer.contact': 'Contact Info',
    'footer.followUs': 'Follow Us',
    'footer.rights': 'All rights reserved.',
    'footer.location': 'Location',
    'footer.phone': 'Phone',
    'footer.email': 'Email',

    // Availability
    'availability.title': 'Class Schedule',
    'availability.subtitle': 'Available Time Slots',
    'availability.description': 'Choose from our available time slots for your Al-Quran learning sessions.',
    'availability.available': 'Available',
    'availability.booked': 'Booked',
    'availability.morning': 'Morning',
    'availability.afternoon': 'Afternoon',
    'availability.evening': 'Evening',
    'availability.pricing_title': 'Pricing',
    'availability.price_hourly': '$30',
    'availability.price_half_hour': '$15',
    'availability.book_now': 'Book Now',
    'availability.loading': 'Loading availability...',
    'availability.monday': 'Monday',
    'availability.tuesday': 'Tuesday',
    'availability.wednesday': 'Wednesday',
    'availability.thursday': 'Thursday',
    'availability.friday': 'Friday',
    'availability.saturday': 'Saturday',
    'availability.sunday': 'Sunday',
  },
  ms: {
    // Navigation
    'nav.home': 'Utama',
    'nav.about': 'Tentang',
    'nav.services': 'Perkhidmatan',
    'nav.blog': 'Blog',
    'nav.contact': 'Hubungi',
    'nav.admin': 'Admin',
    
    // Hero
    'hero.title': 'Belajar Al-Quran bersama',
    'hero.subtitle': 'Ustazah Norazah',
    'hero.description': 'Guru Al-Quran wanita profesional di Singapura yang menawarkan pendidikan Islam yang dipersonalisasi untuk wanita dan kanak-kanak. Belajar bacaan Al-Quran, Tajwid, dan pengajian Islam dalam persekitaran yang selesa dan menyokong.',
    'hero.cta': 'Mula Belajar Hari Ini',
    'hero.experience': '15+ Tahun Pengalaman',
    'hero.students': '500+ Pelajar Berpuas Hati',
    'hero.certified': 'Guru Bertauliah',
    
    // Common
    'common.bookClass': 'Tempah Kelas',
    'common.callNow': 'Hubungi Sekarang',
    'common.readMore': 'Baca Lagi',
    'common.backToHome': '← Kembali ke Utama',
    'common.backToBlog': '← Kembali ke Blog',
    'common.loading': 'Memuatkan...',
    'common.learnMore': 'Ketahui Lebih Lanjut',
    'common.getStarted': 'Mulakan',
    'common.error': 'Tidak dapat memuatkan kandungan. Sila cuba lagi kemudian.',

    // Services
    'services.title': 'Perkhidmatan Kami',
    'services.subtitle': 'Program Pembelajaran Al-Quran yang Komprehensif',
    'services.description': 'Kami menawarkan pelbagai program khusus yang direka untuk membantu anda menguasai seni bacaan dan pemahaman Al-Quran yang indah.',
    'services.individual.title': 'Kelas Individu',
    'services.individual.description': 'Pelajaran Al-Quran peribadi satu-dengan-satu yang disesuaikan dengan kadar dan matlamat pembelajaran anda.',
    'services.group.title': 'Kelas Berkumpulan',
    'services.group.description': 'Belajar bersama pelajar lain dalam kumpulan kecil untuk pengalaman pembelajaran kolaboratif.',
    'services.online.title': 'Kelas Dalam Talian',
    'services.online.description': 'Pelajaran Al-Quran dalam talian yang mudah dari keselesaan rumah anda melalui panggilan video.',
    'services.tajweed.title': 'Kelas Tajwid',
    'services.tajweed.description': 'Kuasai sebutan yang betul dan hukum bacaan Al-Quran dengan program Tajwid komprehensif kami.',
    'services.memorization.title': 'Hafazan Al-Quran',
    'services.memorization.description': 'Program Hifz berstruktur untuk membantu anda menghafaz Al-Quran dengan teknik dan kaedah ingatan yang betul.',
    'services.children.title': 'Program Kanak-kanak',
    'services.children.description': 'Kelas Al-Quran yang menyeronokkan dan menarik yang direka khusus untuk pelajar muda.',
    'services.intensive.title': 'Kursus Intensif',
    'services.intensive.description': 'Program pembelajaran dipercepatkan untuk pelajar berdedikasi yang ingin maju dengan cepat.',

    // Service Features
    'services.individual.feature1': 'Pelan pembelajaran disesuaikan',
    'services.individual.feature2': 'Jadual fleksibel',
    'services.individual.feature3': 'Perhatian peribadi',
    'services.individual.feature4': 'Penjejakan kemajuan',
    'services.group.feature1': 'Saiz kelas kecil (3-5 pelajar)',
    'services.group.feature2': 'Pembelajaran interaktif',
    'services.group.feature3': 'Motivasi rakan sebaya',
    'services.group.feature4': 'Kos efektif',
    'services.online.feature1': 'Belajar dari mana-mana',
    'services.online.feature2': 'Sumber digital',
    'services.online.feature3': 'Sesi dirakam',
    'services.online.feature4': 'Sokongan teknikal',
    'services.tajweed.feature1': 'Panduan sebutan terperinci',
    'services.tajweed.feature2': 'Sesi latihan audio',
    'services.tajweed.feature3': 'Teknik pembetulan',
    'services.tajweed.feature4': 'Sijil tersedia',
    'services.children.feature1': 'Kaedah sesuai umur',
    'services.children.feature2': 'Aktiviti interaktif',
    'services.children.feature3': 'Sistem ganjaran',
    'services.children.feature4': 'Laporan kemajuan ibu bapa',
    'services.intensive.feature1': 'Pembelajaran pantas',
    'services.intensive.feature2': 'Sesi harian tersedia',
    'services.intensive.feature3': 'Pendekatan berorientasikan matlamat',
    'services.intensive.feature4': 'Bahan komprehensif',

    // Service Pricing & Duration
    'services.individual.price': 'Dari $80/sesi',
    'services.individual.duration': '60 minit',
    'services.group.price': 'Dari $45/sesi',
    'services.group.duration': '90 minit',
    'services.online.price': 'Dari $60/sesi',
    'services.online.duration': '60 minit',
    'services.tajweed.price': 'Dari $90/sesi',
    'services.tajweed.duration': '75 minit',
    'services.children.price': 'Dari $50/sesi',
    'services.children.duration': '45 minit',
    'services.intensive.price': 'Harga tersuai',
    'services.intensive.duration': 'Fleksibel',

    // Service Labels
    'services.mostPopular': 'Paling Popular',
    'services.bookNow': 'Tempah Sekarang',
    'services.notSure': 'Tidak pasti program mana yang sesuai untuk anda?',
    'services.consultation': 'Jadualkan perundingan percuma untuk membincangkan matlamat pembelajaran anda dan mencari program yang sempurna. Atau terokai blog pendidikan kami untuk tips dan panduan pembelajaran.',
    'services.freeConsultation': 'Perundingan Percuma',
    'services.learningResources': 'Sumber Pembelajaran',

    // About
    'about.title': 'Tentang Ustazah Norazah',
    'about.subtitle': 'Pendidik Islam yang Berdedikasi',
    'about.description': 'Dengan pengalaman lebih 15 tahun dalam pendidikan Islam, Ustazah Norazah komited menyediakan pendidikan Al-Quran berkualiti.',
    'about.greeting': 'Assalamu Alaikum! Saya Ustazah Norazah, seorang guru Al-Quran yang berdedikasi di Punggol, Singapura. Dengan pengalaman lebih 15 tahun dalam pendidikan Islam, saya telah mendapat keistimewaan membimbing ratusan pelajar dalam perjalanan mereka untuk berhubung dengan Al-Quran.',
    'about.philosophy': 'Falsafah pengajaran saya berpusat pada mewujudkan persekitaran yang memupuk di mana pelajar dari semua peringkat umur boleh belajar mengikut kadar mereka sendiri sambil mengembangkan kasih sayang dan pemahaman yang mendalam terhadap Al-Quran. Saya percaya bahawa setiap pelajar adalah unik, dan saya menyesuaikan pendekatan saya untuk memenuhi keperluan individu mereka.',
    'about.passion': 'Keghairahan saya terhadap Al-Quran telah menginspirasi saya untuk mendedikasikan diri sepenuhnya untuk meningkatkan bacaan dan pemahaman saya, sambil juga berkongsi kasih sayang terhadap Al-Quran dengan membimbing dan menginspirasi orang lain dalam perjalanan mereka sendiri.',
    'about.experience': 'Tahun Pengalaman Mengajar',
    'about.students': 'Pelajar Diajar',
    'about.programs': 'Program Ditawarkan',

    // About Qualifications
    'about.qualification1': 'Guru Al-Quran Bertauliah (Ijazah)',
    'about.qualification2': 'Sarjana Muda Pengajian Islam',
    'about.qualification3': 'Sijil Pakar Tajwid',
    'about.qualification4': '15+ Tahun Pengalaman Mengajar',
    'about.qualification5': 'Fasih dalam Bahasa Inggeris, Melayu & Arab',

    // About Achievements
    'about.studentsTaught': 'Pelajar Diajar',
    'about.quranCompletions': 'Khatam Al-Quran',
    'about.yearsExperience': 'Tahun Pengalaman',
    'about.studentRating': 'Penilaian Pelajar',

    // About Teaching Approach
    'about.teachingApproach': 'Pendekatan Pengajaran Saya',
    'about.approachDescription': 'Saya percaya dalam menggabungkan kaedah pengajaran Islam tradisional dengan pendekatan pendidikan moden untuk mencipta pengalaman pembelajaran yang berkesan dan menarik.',
    'about.personalizedTitle': 'Pembelajaran Peribadi',
    'about.personalizedDesc': 'Setiap pelajar menerima pelan pembelajaran yang disesuaikan berdasarkan tahap semasa, matlamat, dan gaya pembelajaran mereka.',
    'about.patientTitle': 'Sabar & Menyokong',
    'about.patientDesc': 'Mencipta persekitaran yang selesa di mana pelajar berasa yakin untuk belajar dan membuat kesilapan.',
    'about.holisticTitle': 'Pendidikan Holistik',
    'about.holisticDesc': 'Selain bacaan, kami fokus pada pemahaman, kerohanian, dan aplikasi praktikal dalam kehidupan seharian.',
    'about.modernTitle': 'Kaedah Moden',
    'about.modernDesc': 'Menggabungkan pengajaran Islam tradisional dengan teknik pendidikan moden dan teknologi.',
    'about.certifiedTeacher': 'Guru Bertauliah',
    'about.quote': '"Mengajar Al-Quran bukan sahaja profesion saya, ia adalah keghairahan dan panggilan saya."',

    // Testimonials
    'testimonials.title': 'Kata Pelajar Kami',
    'testimonials.subtitle': 'Testimoni dari Komuniti Pembelajaran Kami',

    // Contact
    'contact.title': 'Hubungi Kami',
    'contact.subtitle': 'Bersedia Memulakan Perjalanan Al-Quran Anda?',
    'contact.description': 'Hubungi kami hari ini untuk menjadualkan pelajaran pertama anda atau untuk mengetahui lebih lanjut tentang program kami.',
    'contact.name': 'Nama Penuh',
    'contact.email': 'Alamat E-mel',
    'contact.phone': 'Nombor Telefon',
    'contact.message': 'Mesej Anda',
    'contact.send': 'Hantar Mesej',
    'contact.info': 'Maklumat Hubungan',
    'contact.address': 'Alamat',
    'contact.hours': 'Waktu Mengajar',
    'contact.sendMessage': 'Hantar Mesej',
    'contact.respond': 'Kami akan membalas dalam masa 24 jam',
    'contact.location': 'Lokasi',
    'contact.locationDetails1': 'Punggol Central, Singapura',
    'contact.locationDetails2': 'Lawatan rumah tersedia',
    'contact.phoneDetails1': '+65 8835 2027',
    'contact.phoneDetails2': 'WhatsApp tersedia',
    'contact.emailDetails1': '<EMAIL>',
    'contact.emailDetails2': 'Respons dalam masa 24 jam',
    'contact.hoursDetails1': 'Isnin-Jumaat: 2PM - 8PM',
    'contact.hoursDetails2': 'Sabtu-Ahad: 9AM - 6PM',
    'contact.service': 'Perkhidmatan Berminat',
    'contact.selectService': 'Pilih perkhidmatan',
    'contact.studentAge': 'Umur Pelajar (Pilihan)',
    'contact.preferredTime': 'Masa Pilihan (Pilihan)',
    'contact.messagePlaceholder': 'Beritahu kami tentang matlamat pembelajaran, tahap pengalaman, dan sebarang keperluan khusus anda...',
    'contact.submitting': 'Menghantar...',
    'contact.success': 'Mesej berjaya dihantar!',
    'contact.error': 'Gagal menghantar mesej. Sila cuba lagi.',

    // Hadith
    'hadith.title': 'Hadis Minggu Ini',

    // Footer
    'footer.description': 'Guru Al-Quran wanita profesional di Singapura yang menawarkan pendidikan Islam yang dipersonalisasi untuk wanita dan kanak-kanak.',
    'footer.quickLinks': 'Pautan Pantas',
    'footer.services': 'Perkhidmatan',
    'footer.contact': 'Maklumat Hubungan',
    'footer.followUs': 'Ikuti Kami',
    'footer.rights': 'Hak cipta terpelihara.',
    'footer.location': 'Lokasi',
    'footer.phone': 'Telefon',
    'footer.email': 'E-mel',

    // Availability
    'availability.title': 'Jadual Kelas',
    'availability.subtitle': 'Slot Masa Tersedia',
    'availability.description': 'Pilih dari slot masa tersedia untuk sesi pembelajaran Al-Quran anda.',
    'availability.available': 'Tersedia',
    'availability.booked': 'Ditempah',
    'availability.morning': 'Pagi',
    'availability.afternoon': 'Petang',
    'availability.evening': 'Malam',
    'availability.pricing_title': 'Harga',
    'availability.price_hourly': '$30',
    'availability.price_half_hour': '$15',
    'availability.book_now': 'Tempah Sekarang',
    'availability.loading': 'Memuatkan ketersediaan...',
    'availability.monday': 'Isnin',
    'availability.tuesday': 'Selasa',
    'availability.wednesday': 'Rabu',
    'availability.thursday': 'Khamis',
    'availability.friday': 'Jumaat',
    'availability.saturday': 'Sabtu',
    'availability.sunday': 'Ahad',
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('en');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const updateLanguageFromURL = () => {
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        const urlLanguage = currentPath.split('/')[1] as Language;
        if (urlLanguage === 'en' || urlLanguage === 'ms') {
          setLanguage(urlLanguage);
          localStorage.setItem('language', urlLanguage);
          return;
        }
      }

      // Fallback to saved language from localStorage
      if (typeof window !== 'undefined') {
        const savedLanguage = localStorage.getItem('language') as Language;
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ms')) {
          setLanguage(savedLanguage);
        }
      }
    };

    // Initial language detection
    updateLanguageFromURL();

    // Listen for URL changes (for SPA navigation)
    const handlePopState = () => {
      updateLanguageFromURL();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', handlePopState);

      // Also listen for pushstate/replacestate (for programmatic navigation)
      const originalPushState = window.history.pushState;
      const originalReplaceState = window.history.replaceState;

      window.history.pushState = function(...args) {
        originalPushState.apply(window.history, args);
        setTimeout(updateLanguageFromURL, 0);
      };

      window.history.replaceState = function(...args) {
        originalReplaceState.apply(window.history, args);
        setTimeout(updateLanguageFromURL, 0);
      };

      return () => {
        window.removeEventListener('popstate', handlePopState);
        window.history.pushState = originalPushState;
        window.history.replaceState = originalReplaceState;
      };
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    if (mounted) {
      localStorage.setItem('language', lang);
    }
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}