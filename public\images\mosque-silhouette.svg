<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="mosqueGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#2D3748;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1A202C;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Sky background -->
  <rect width="400" height="300" fill="url(#skyGradient)"/>
  
  <!-- Main mosque structure -->
  <g fill="url(#mosqueGradient)">
    <!-- Central dome -->
    <ellipse cx="200" cy="120" rx="60" ry="40"/>
    
    <!-- Main building -->
    <rect x="140" y="120" width="120" height="100"/>
    
    <!-- Side buildings -->
    <rect x="80" y="140" width="60" height="80"/>
    <rect x="260" y="140" width="60" height="80"/>
    
    <!-- Minarets -->
    <rect x="60" y="80" width="20" height="140"/>
    <rect x="320" y="80" width="20" height="140"/>
    
    <!-- Minaret tops -->
    <ellipse cx="70" cy="80" rx="15" ry="10"/>
    <ellipse cx="330" cy="80" rx="15" ry="10"/>
    
    <!-- Central minaret -->
    <rect x="190" y="60" width="20" height="60"/>
    <ellipse cx="200" cy="60" rx="15" ry="10"/>
    
    <!-- Arched windows -->
    <path d="M 160 160 Q 170 150 180 160 L 180 200 L 160 200 Z" fill="#4A5568"/>
    <path d="M 220 160 Q 230 150 240 160 L 240 200 L 220 200 Z" fill="#4A5568"/>
    
    <!-- Main entrance arch -->
    <path d="M 180 180 Q 200 160 220 180 L 220 220 L 180 220 Z" fill="#4A5568"/>
    
    <!-- Crescent moon on central dome -->
    <path d="M 195 100 Q 200 95 205 100 Q 200 105 195 100" fill="#FFD700"/>
    
    <!-- Stars -->
    <circle cx="120" cy="40" r="2" fill="#FFD700"/>
    <circle cx="280" cy="50" r="2" fill="#FFD700"/>
    <circle cx="350" cy="30" r="2" fill="#FFD700"/>
    <circle cx="50" cy="35" r="2" fill="#FFD700"/>
  </g>
</svg>
