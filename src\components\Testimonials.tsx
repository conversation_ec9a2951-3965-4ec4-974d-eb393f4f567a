'use client';

import { useState } from 'react';
import { Star, ChevronLeft, ChevronRight, Quote } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const Testimonials = () => {
  const { t } = useLanguage();
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      name: '<PERSON><PERSON><PERSON>',
      role: 'Parent of 8-year-old student',
      location: 'Punggol, Singapore',
      rating: 5,
      text: '<PERSON><PERSON><PERSON><PERSON> has been incredible with my daughter. Her patience and gentle approach have helped my child develop a genuine love for the Quran. The progress in just 6 months has been remarkable!',
      highlight: 'Remarkable progress in 6 months'
    },
    {
      name: '<PERSON>',
      role: 'Adult Student',
      location: 'Sengkang, Singapore',
      rating: 5,
      text: 'As a working adult, I needed flexible timing and personalized attention. <PERSON><PERSON><PERSON><PERSON> accommodated my schedule and helped me improve my Tajweed significantly. Highly recommended!',
      highlight: 'Flexible and accommodating'
    },
    {
      name: '<PERSON><PERSON>',
      role: 'University Student',
      location: 'Punggol, Singapore',
      rating: 5,
      text: 'The online classes are so convenient! Despite being virtual, <PERSON><PERSON><PERSON><PERSON> maintains the same quality of teaching. I\'ve completed 5 Juz under her guidance.',
      highlight: 'Completed 5 Juz online'
    },
    {
      name: '<PERSON>m <PERSON>',
      role: 'Mother of 3',
      location: 'Hougang, Singapore',
      rating: 5,
      text: 'All three of my children learn with Ustazah <PERSON>zah. She adapts her teaching style for each child\'s personality and learning pace. We couldn\'t be happier!',
      highlight: '<PERSON>pts to each child\'s needs'
    },
    {
      name: '<PERSON> <PERSON>',
      role: 'Teenager',
      location: 'Punggol, Singapore',
      rating: 5,
      text: 'I was struggling with Arabic pronunciation until I started with Ustazah Norazah. Her Tajweed classes are amazing and she makes learning fun and engaging.',
      highlight: 'Makes learning fun and engaging'
    },
    {
      name: 'Khadijah Omar',
      role: 'New Muslim',
      location: 'Serangoon, Singapore',
      rating: 5,
      text: 'As a new Muslim, I was nervous about learning the Quran. Ustazah Norazah created such a welcoming environment and guided me with incredible patience and understanding.',
      highlight: 'Welcoming for new Muslims'
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section className="py-24 bg-gradient-to-br from-emerald-50 via-slate-50 to-green-50 dark:from-slate-800 dark:via-slate-900 dark:to-slate-800 transition-colors duration-300 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 arabic-pattern opacity-30 dark:opacity-10"></div>

      {/* Decorative Islamic Elements */}
      <div className="absolute top-10 left-10 opacity-5 dark:opacity-3 transform rotate-45">
        <img src="/images/quran-book.svg" alt="" className="w-20 h-24" />
      </div>
      <div className="absolute bottom-10 right-10 opacity-5 dark:opacity-3 transform -rotate-12">
        <img src="/images/mosque-silhouette.svg" alt="" className="w-28 h-20" />
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-8 animate-fade-in-down">
            <span>{t('testimonials.title')}</span>
          </div>
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-slate-100 mb-6 leading-tight animate-fade-in-up animate-stagger-1">
            {t('testimonials.title')}
          </h2>
          <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animate-stagger-2">
            {t('testimonials.subtitle')}
          </p>
        </div>

        {/* Main Testimonial */}
        <div className="relative max-w-5xl mx-auto mb-20 animate-scale-in animate-stagger-3">
          <div className="bg-white dark:bg-slate-800 rounded-3xl shadow-2xl p-12 md:p-16 relative border border-slate-200 dark:border-slate-600 card-hover-lift">
            {/* Quote Icon */}
            <div className="absolute -top-8 left-12">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-green-600 rounded-full flex items-center justify-center shadow-xl">
                <Quote className="w-8 h-8 text-white" />
              </div>
            </div>

            {/* Testimonial Content */}
            <div className="pt-8">
              {/* Stars */}
              <div className="flex justify-center mb-8">
                <div className="flex space-x-1">
                  {renderStars(testimonials[currentTestimonial].rating)}
                </div>
              </div>

              {/* Text */}
              <blockquote className="text-2xl md:text-3xl lg:text-4xl text-slate-700 dark:text-slate-300 text-center mb-10 leading-relaxed font-medium">
                "{testimonials[currentTestimonial].text}"
              </blockquote>

              {/* Highlight */}
              <div className="text-center mb-8">
                <span className="bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-6 py-3 rounded-full font-semibold text-lg">
                  {testimonials[currentTestimonial].highlight}
                </span>
              </div>

              {/* Author */}
              <div className="text-center">
                <div className="font-bold text-slate-900 dark:text-slate-100 text-2xl mb-2">
                  {testimonials[currentTestimonial].name}
                </div>
                <div className="text-slate-600 dark:text-slate-400 text-lg mb-1">
                  {testimonials[currentTestimonial].role}
                </div>
                <div className="text-slate-500 dark:text-slate-500 font-medium">
                  {testimonials[currentTestimonial].location}
                </div>
              </div>
            </div>

            {/* Navigation Buttons */}
            <button
              onClick={prevTestimonial}
              className="absolute left-6 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-white dark:bg-slate-700 shadow-xl rounded-full flex items-center justify-center hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-300 hover:scale-110 border border-slate-200 dark:border-slate-600"
            >
              <ChevronLeft className="w-8 h-8 text-slate-600 dark:text-slate-300" />
            </button>

            <button
              onClick={nextTestimonial}
              className="absolute right-6 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-white dark:bg-slate-700 shadow-xl rounded-full flex items-center justify-center hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-300 hover:scale-110 border border-slate-200 dark:border-slate-600"
            >
              <ChevronRight className="w-8 h-8 text-slate-600 dark:text-slate-300" />
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                  index === currentTestimonial ? 'bg-emerald-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 card-hover-lift animate-fade-in-up animate-stagger-4">
            <div className="text-4xl font-bold text-emerald-600 mb-2">4.9/5</div>
            <div className="text-gray-600 dark:text-slate-400">Average Rating</div>
            <div className="flex justify-center mt-2">
              {renderStars(5)}
            </div>
          </div>

          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 card-hover-lift animate-fade-in-up animate-stagger-5">
            <div className="text-4xl font-bold text-green-600 mb-2">200+</div>
            <div className="text-gray-600 dark:text-slate-400">Happy Students</div>
          </div>

          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 card-hover-lift animate-fade-in-up animate-stagger-6">
            <div className="text-4xl font-bold text-purple-600 mb-2">98%</div>
            <div className="text-gray-600 dark:text-slate-400">Completion Rate</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
