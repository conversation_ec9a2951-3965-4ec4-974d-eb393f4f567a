# 🚀 Netlify Deployment Guide

## 🔐 **ADMIN DASHBOARD ACCESS**

### **Admin URL:**
```
https://yourdomain.netlify.app/admin
```

### **Login Credentials:**
- **Password**: `ustazah2025`
- **Security**: Session-based (expires when browser closes)
- **Access**: Password-protected admin dashboard

### **Admin Features:**
- ✅ **View all leads** from contact form submissions
- ✅ **Export to CSV** for external tools
- ✅ **Lead details** with contact information
- ✅ **Statistics** (total, monthly, daily leads)
- ✅ **Quick actions** (email, phone links)

## 🌐 **NETLIFY DEPLOYMENT STEPS**

### **Step 1: Connect GitHub Repository**
1. **Login to Netlify**: Go to https://netlify.com
2. **New Site**: Click "New site from Git"
3. **Connect GitHub**: Authorize Netlify to access your repositories
4. **Select Repository**: Choose `norazah-alquran2025`
5. **Deploy Settings**:
   - **Branch**: `main`
   - **Build Command**: `npm run build`
   - **Publish Directory**: `.next`

### **Step 2: Configure Environment Variables**
In Netlify dashboard, go to **Site Settings > Environment Variables** and add:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://uhrvopnjuuphyxpmikcg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVocnZvcG5qdXVwaHl4cG1pa2NnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTQ3MjEsImV4cCI6MjA2NjEzMDcyMX0.gm2KfnxZL5d4RmyAjsXc1MtT6_JSD8F0pJwfDdUsR50

# Email Notifications (Optional)
RESEND_API_KEY=re_CUo1Cgjw_3w6G3458GJzP54eKgF1Mnj55
FROM_EMAIL=<EMAIL>
NOTIFICATION_EMAIL=<EMAIL>

# Admin Dashboard Security
ADMIN_PASSWORD=ustazah2025

# Google Analytics (Optional)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

### **Step 3: Deploy**
1. **Click Deploy**: Netlify will build and deploy your site
2. **Wait for Build**: Usually takes 2-3 minutes
3. **Get URL**: Netlify provides a random URL (e.g., `amazing-site-123.netlify.app`)
4. **Custom Domain**: Optional - add your own domain

## 🔒 **SECURITY RECOMMENDATIONS**

### **For Production:**

#### **1. Change Admin Password**
Update the `ADMIN_PASSWORD` environment variable to something secure:
```env
ADMIN_PASSWORD=YourSecurePassword123!
```

#### **2. Use Strong Password**
- **Minimum 12 characters**
- **Mix of letters, numbers, symbols**
- **Unique password** not used elsewhere

#### **3. Additional Security Options**

**Option A: IP Restriction (Netlify Pro)**
- Restrict admin access to specific IP addresses
- Go to Site Settings > Access Control

**Option B: Netlify Identity (Advanced)**
- Full user authentication system
- Email-based login
- More complex setup

**Option C: Basic Auth (Simple)**
Add to `netlify.toml`:
```toml
[[redirects]]
  from = "/admin/*"
  to = "/admin/:splat"
  status = 200
  force = true
  headers = {X-Frame-Options = "DENY", X-XSS-Protection = "1; mode=block"}
```

## 📊 **ADMIN DASHBOARD FEATURES**

### **Dashboard Overview:**
- **Total Leads**: Count of all contact form submissions
- **Monthly Stats**: Leads received this month
- **Daily Stats**: Leads received today
- **Recent Leads Table**: All leads with details

### **Lead Information Displayed:**
- ✅ **Contact Details**: Name, email, phone
- ✅ **Service Interest**: Which service they want
- ✅ **Student Details**: Age, preferred time (if provided)
- ✅ **Message**: Their full inquiry
- ✅ **Submission Time**: When they contacted you
- ✅ **Quick Actions**: Email and call buttons

### **Export Functionality:**
- **CSV Export**: Download all leads for Excel/Google Sheets
- **Includes**: All lead data with timestamps
- **Format**: Professional, ready for CRM import

## 🌐 **WEBSITE URLS AFTER DEPLOYMENT**

### **Main Website:**
```
https://yourdomain.netlify.app/
```

### **Admin Dashboard:**
```
https://yourdomain.netlify.app/admin
```

### **Blog Pages:**
```
https://yourdomain.netlify.app/blog
```

## 📧 **EMAIL NOTIFICATIONS**

### **Current Status:**
- ✅ **System Ready**: Email templates and API integration complete
- ⚠️ **Domain Issue**: Gmail domain not verified in Resend
- ✅ **Leads Saved**: Contact forms work regardless of email status

### **To Fix Email Notifications:**
1. **Option A**: Verify Gmail domain in Resend dashboard
2. **Option B**: Use Resend's default domain for FROM_EMAIL
3. **Option C**: Use a custom domain you own

### **Email Features When Active:**
- **Instant notifications** when someone submits contact form
- **Professional HTML templates** with your branding
- **Complete lead information** in every email
- **Quick action buttons** for immediate response

## 🎯 **TESTING YOUR DEPLOYMENT**

### **1. Test Website:**
- Visit your Netlify URL
- Check all pages load correctly
- Test contact form submission

### **2. Test Admin Dashboard:**
- Go to `/admin`
- Enter password: `ustazah2025`
- Verify leads appear
- Test CSV export

### **3. Test Contact Form:**
- Fill out contact form on main website
- Check if lead appears in admin dashboard
- Verify all information is captured

## 🔧 **TROUBLESHOOTING**

### **Build Errors:**
- Check environment variables are set correctly
- Ensure all required variables are present
- Check build logs in Netlify dashboard

### **Admin Access Issues:**
- Verify `ADMIN_PASSWORD` environment variable
- Clear browser cache and cookies
- Try incognito/private browsing mode

### **Contact Form Not Working:**
- Check Supabase environment variables
- Verify database permissions
- Check browser console for errors

### **Email Notifications Not Working:**
- Check `RESEND_API_KEY` is correct
- Verify `FROM_EMAIL` domain
- Check Netlify function logs

## 🎉 **CONGRATULATIONS!**

Your **Al-Quran teaching website** is now **live on Netlify** with:

### **✅ Live Features:**
- 🌿 **Professional website** with green psychology design
- 📊 **Complete lead capture** system
- 🔐 **Secure admin dashboard** with password protection
- 📧 **Email notification system** (ready to activate)
- 📱 **Mobile-responsive** design
- 🚀 **Production-ready** implementation

### **🎯 Admin Access:**
- **URL**: `https://yourdomain.netlify.app/admin`
- **Password**: `ustazah2025`
- **Features**: Lead management, CSV export, statistics

Your **Al-Quran teaching business** is now **live and ready** to capture leads professionally! 🕌✨

## 📞 **SUPPORT**

If you need help:
1. **Check this guide** for common solutions
2. **Review Netlify build logs** for technical issues
3. **Test locally first** to isolate problems
4. **Contact support** if needed

**Your website is ready for business!** 🚀
