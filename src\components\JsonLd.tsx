'use client';

import { useEffect } from 'react';

const JsonLd = () => {
  useEffect(() => {
    // Only add JSON-LD on client side to prevent hydration mismatch
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.innerHTML = JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'EducationalOrganization',
      name: 'Ustazah Norazah Quran Academy',
      description: 'Professional Al-Quran teaching services in Singapore',
      url: 'https://ustazah-norazah.com',
      logo: 'https://ustazah-norazah.com/logo.png',
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+6588352027',
        contactType: 'customer service',
        areaServed: 'SG',
        availableLanguage: ['English', 'Malay', 'Arabic']
      },
      address: {
        '@type': 'PostalAddress',
        addressLocality: 'Singapore',
        addressRegion: 'Singapore',
        addressCountry: 'SG'
      },
      founder: {
        '@type': 'Person',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        jobTitle: 'Al-Quran Teacher',
        description: 'Experienced female Al-Quran teacher specializing in Tajweed and Islamic studies'
      },
      offers: {
        '@type': 'Offer',
        category: 'Education',
        description: 'Al-Quran classes, Tajweed lessons, and Islamic studies'
      }
    });

    document.head.appendChild(script);

    // Cleanup function to remove the script when component unmounts
    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  return null; // This component doesn't render anything visible
};

export default JsonLd;
