import { NextRequest, NextResponse } from 'next/server';
import { sendNewLeadNotification } from '@/lib/email';
import { ContactLead } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const lead: ContactLead = await request.json();
    
    // Validate required fields
    if (!lead.full_name || !lead.email || !lead.phone || !lead.service || !lead.message) {
      return NextResponse.json(
        { error: 'Missing required lead information' },
        { status: 400 }
      );
    }

    // Send email notification
    const result = await sendNewLeadNotification(lead);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Email notification sent successfully',
      emailId: result?.id 
    });
  } catch (error) {
    console.error('Error sending email notification:', error);
    return NextResponse.json(
      { error: 'Failed to send email notification' },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({ 
    status: 'Email notification service is running',
    timestamp: new Date().toISOString()
  });
}
