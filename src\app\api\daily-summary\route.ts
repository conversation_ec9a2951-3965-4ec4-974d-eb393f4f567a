import { NextRequest, NextResponse } from 'next/server';
import { getContactLeads } from '@/lib/supabase';
import { sendDailyLeadSummary } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    // Get today's leads
    const allLeads = await getContactLeads();
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todaysLeads = allLeads.filter(lead => {
      if (!lead.created_at) return false;
      const leadDate = new Date(lead.created_at);
      leadDate.setHours(0, 0, 0, 0);
      return leadDate.getTime() === today.getTime();
    });

    if (todaysLeads.length === 0) {
      return NextResponse.json({ 
        message: 'No leads today - no summary sent',
        leadsCount: 0
      });
    }

    // Send daily summary
    const result = await sendDailyLeadSummary(todaysLeads);
    
    return NextResponse.json({ 
      success: true, 
      message: `Daily summary sent for ${todaysLeads.length} leads`,
      leadsCount: todaysLeads.length,
      emailId: result?.id 
    });
  } catch (error) {
    console.error('Error sending daily summary:', error);
    return NextResponse.json(
      { error: 'Failed to send daily summary' },
      { status: 500 }
    );
  }
}

// Manual trigger endpoint
export async function GET() {
  try {
    // Get yesterday's leads for manual testing
    const allLeads = await getContactLeads();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    
    const yesterdaysLeads = allLeads.filter(lead => {
      if (!lead.created_at) return false;
      const leadDate = new Date(lead.created_at);
      leadDate.setHours(0, 0, 0, 0);
      return leadDate.getTime() === yesterday.getTime();
    });

    return NextResponse.json({ 
      message: 'Daily summary endpoint is ready',
      yesterdaysLeads: yesterdaysLeads.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in daily summary GET:', error);
    return NextResponse.json(
      { error: 'Failed to check daily summary' },
      { status: 500 }
    );
  }
}
