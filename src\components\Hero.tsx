'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Star, MapPin, Users, Award, ChevronRight, Play, X } from 'lucide-react';
import { useTranslations } from 'next-intl';

const Hero = () => {
  const [showVideo, setShowVideo] = useState(false);
  const t = useTranslations('hero');

  const stats = [
    { icon: Users, value: '500+', label: t('students') },
    { icon: Award, value: t('experience'), label: '' },
    { icon: Star, value: '4.9', label: 'Average Rating' },
  ];

  return (
    <section className="relative bg-gradient-to-br from-slate-50 via-emerald-50 to-green-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 overflow-hidden transition-colors duration-300">
      {/* Background Pattern */}
      <div className="absolute inset-0 islamic-pattern opacity-5 dark:opacity-0"></div>

      {/* Dark Mode Islamic Pattern */}
      <div className="absolute inset-0 opacity-0 dark:opacity-10 transition-opacity duration-300">
        <div className="absolute inset-0 star-crescent"></div>
        <div className="absolute inset-0 mosque-silhouette opacity-30"></div>
      </div>

      {/* Decorative Islamic Elements */}
      <div className="absolute top-10 right-10 opacity-10 dark:opacity-15 transform rotate-12">
        <img src="/images/mosque-silhouette.svg" alt="" className="w-32 h-24" />
      </div>
      <div className="absolute bottom-10 left-10 opacity-10 dark:opacity-15 transform -rotate-12">
        <img src="/images/islamic-pattern.svg" alt="" className="w-24 h-24" />
      </div>
      <div className="absolute top-1/3 left-1/4 opacity-5 dark:opacity-10">
        <img src="/images/quran-book.svg" alt="" className="w-16 h-20" />
      </div>

      {/* Additional Islamic Elements for Dark Mode */}
      <div className="absolute top-1/4 right-1/4 opacity-0 dark:opacity-8 transform rotate-45">
        <div className="w-12 h-12 border-2 border-emerald-400/30 rounded-full"></div>
      </div>
      <div className="absolute bottom-1/4 right-1/3 opacity-0 dark:opacity-8 transform -rotate-12">
        <div className="w-8 h-8 bg-emerald-400/20 rounded-full"></div>
      </div>
      <div className="absolute top-2/3 left-1/6 opacity-0 dark:opacity-8">
        <div className="w-6 h-6 border border-emerald-400/40 transform rotate-45"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-12 gap-8 items-center min-h-screen py-24 pt-32">
          {/* Left Column - Content */}
          <div className="lg:col-span-7 text-center lg:text-left">

            {/* Main Heading */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-slate-900 dark:text-slate-100 leading-[1.1] mb-8 animate-fade-in-left text-professional-lg">
              {t('title')}{' '}
              <span className="gradient-text-elegant">
                {t('subtitle')}
              </span>
            </h1>

            {/* Subheading */}
            <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-10 leading-relaxed max-w-2xl mx-auto lg:mx-0 animate-fade-in-left animate-stagger-1">
              {t('description')}
            </p>

            {/* Arabic Quote */}
            <div className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-2xl p-8 mb-10 border border-slate-200 dark:border-slate-600 shadow-lg animate-fade-in-up animate-stagger-2">
              <p className="arabic-text text-3xl lg:text-4xl text-slate-800 dark:text-slate-200 mb-4 leading-relaxed">
                وَرَتِّلِ الْقُرْآنَ تَرْتِيلًا
              </p>
              <p className="text-slate-600 dark:text-slate-400 italic text-lg">
                "And recite the Quran with measured recitation." - Quran 73:4
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 mb-16 animate-fade-in-up animate-stagger-3">
              <Link
                href="#contact"
                className="group btn-professional text-white px-10 py-5 rounded-2xl font-semibold text-lg btn-hover-lift flex items-center justify-center space-x-3 text-professional"
              >
                <span>{t('cta')}</span>
                <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>

              <button
                onClick={() => setShowVideo(true)}
                className="group bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm text-slate-700 dark:text-slate-300 px-10 py-5 rounded-2xl font-semibold text-lg border border-slate-200 dark:border-slate-600 hover:border-emerald-500 hover:text-emerald-600 transition-all duration-300 shadow-lg btn-hover-lift flex items-center justify-center space-x-3"
              >
                <Play className="w-5 h-5 group-hover:scale-110 transition-transform" />
                <span>Watch Introduction</span>
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 lg:gap-12 animate-fade-in-up animate-stagger-4">
              {stats.map((stat, index) => (
                <div key={index} className={`text-center group animate-scale-in-bounce animate-stagger-${index + 5}`}>
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-emerald-100 to-green-100 dark:from-emerald-900/30 dark:to-green-900/30 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <stat.icon className="w-8 h-8 text-emerald-600 dark:text-emerald-400" />
                    </div>
                  </div>
                  <div className="text-3xl lg:text-4xl font-bold text-slate-900 dark:text-slate-100 mb-2">{stat.value}</div>
                  <div className="text-slate-600 dark:text-slate-400 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Column - Image */}
          <div className="lg:col-span-5 relative">
            {/* Location Badge - Centered with Image */}
            <div className="flex justify-center mb-6">
              <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold shadow-sm animate-fade-in-down">
                <MapPin className="w-4 h-4" />
                <span>Punggol, Singapore</span>
              </div>
            </div>

            <div className="relative max-w-lg mx-auto lg:max-w-none">
              {/* Main Image Container */}
              <div className="aspect-[4/5] bg-gradient-to-br from-emerald-50 via-white to-green-50 dark:from-slate-800 dark:via-slate-700 dark:to-slate-800 rounded-3xl shadow-2xl overflow-hidden border border-slate-200 dark:border-slate-600 relative animate-fade-in-right animate-stagger-3">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-600/5 to-green-600/5"></div>
                <div className="w-full h-full flex items-center justify-center relative z-10 p-8">
                  <img
                    src="/images/teacher-placeholder.svg"
                    alt="Ustazah Norazah - Professional Al-Quran Teacher"
                    className="w-4/5 h-4/5 object-contain animate-scale-in-bounce animate-stagger-4"
                  />
                </div>

                {/* Professional overlay */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 animate-fade-in-up animate-stagger-5">
                  <div className="text-right pr-2">
                    <h3 className="text-lg font-bold text-white mb-1">Ustazah Norazah</h3>
                    <p className="text-white/90 font-medium text-sm">Professional Al-Quran Teacher</p>
                    <p className="text-white/70 text-xs mt-1">ARS Certified</p>
                  </div>
                </div>
              </div>

              {/* Floating Achievement Cards */}
              <div className="absolute -top-6 -right-6 bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-100 dark:border-slate-600 animate-bounce-in animate-stagger-6 animate-float">
                <div className="flex items-center space-x-3">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <div>
                    <div className="font-bold text-slate-900 dark:text-slate-100 text-lg">4.9/5</div>
                    <p className="text-xs text-slate-600 dark:text-slate-400">Student Rating</p>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-6 -left-6 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white rounded-2xl shadow-xl p-6 animate-bounce-in animate-stagger-6 animate-float">
                <div className="text-3xl font-bold mb-1">15+</div>
                <p className="text-emerald-100 text-sm font-medium">Years Teaching</p>
              </div>

              {/* Decorative Elements */}
              <div className="absolute top-1/4 -left-4 w-8 h-8 bg-emerald-200 dark:bg-emerald-800/30 rounded-full opacity-60 animate-pulse-slow"></div>
              <div className="absolute bottom-1/3 -right-2 w-6 h-6 bg-emerald-200 dark:bg-emerald-800/30 rounded-full opacity-60 animate-pulse-slow"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      {showVideo && (
        <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full aspect-video relative">
            <button
              onClick={() => setShowVideo(false)}
              className="absolute -top-12 right-0 text-white hover:text-slate-300 transition-colors"
            >
              <X className="w-8 h-8" />
            </button>
            <div className="w-full h-full bg-slate-200 dark:bg-slate-700 rounded-lg flex items-center justify-center">
              <p className="text-slate-600 dark:text-slate-300">Video placeholder - Introduction by Ustazah Norazah</p>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default Hero;
