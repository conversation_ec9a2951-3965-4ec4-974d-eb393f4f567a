import { NextRequest, NextResponse } from 'next/server';

// Import the hadith data from the main route
// Note: In a real application, this would come from a database
const curatedHadiths = [
  {
    id: '1',
    text: 'The Prophet (ﷺ) said: "The best of people are those who learn the Quran and teach it."',
    source: '<PERSON><PERSON><PERSON> 5027',
    collection: '<PERSON><PERSON><PERSON>',
    book: 'Virtues of the Quran',
    hadithNumber: '5027',
    narrator: '<PERSON><PERSON><PERSON> ibn <PERSON>',
    grade: '<PERSON>hih',
    language: 'en' as const
  },
  {
    id: '2',
    text: 'The Prophet (ﷺ) said: "Whoever recites a letter from the Book of Allah, he will be credited with a good deed, and a good deed gets a ten-fold reward."',
    source: '<PERSON><PERSON>` at-Tirmidhi 2910',
    collection: '<PERSON><PERSON>` at-Tirmidhi',
    book: 'Virtues of the Quran',
    hadithNumber: '2910',
    narrator: '<PERSON>\'ud',
    grade: 'Hasan',
    language: 'en' as const
  },
  // Add more hadiths as needed...
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const collection = searchParams.get('collection');
    
    let filteredHadiths = curatedHadiths;
    
    // Filter by collection if specified
    if (collection) {
      filteredHadiths = curatedHadiths.filter(h => 
        h.collection.toLowerCase().includes(collection.toLowerCase())
      );
    }
    
    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedHadiths = filteredHadiths.slice(startIndex, endIndex);
    
    return NextResponse.json({
      success: true,
      data: paginatedHadiths,
      pagination: {
        page,
        limit,
        total: filteredHadiths.length,
        totalPages: Math.ceil(filteredHadiths.length / limit),
        hasNext: endIndex < filteredHadiths.length,
        hasPrev: page > 1
      },
      filters: {
        collection: collection || null
      },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Content-Type': 'application/json',
      }
    });
    
  } catch (error) {
    console.error('Error in hadith list API:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch hadith list',
      message: 'Unable to retrieve hadith list at this time'
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      }
    });
  }
}
