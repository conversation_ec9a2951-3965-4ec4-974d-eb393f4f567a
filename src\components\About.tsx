'use client';

import { Award, BookOpen, Users, CheckCircle, Star } from 'lucide-react';

const About = () => {
  const qualifications = [
    'Certified Quran Teacher (Ijazah)',
    'Bachelor\'s in Islamic Studies',
    'Tajweed Specialist Certification',
    '15+ Years Teaching Experience',
    'Fluent in English, Malay & Arabic'
  ];

  const achievements = [
    { icon: Users, value: '200+', label: 'Students Taught' },
    { icon: BookOpen, value: '50+', label: 'Quran Completions' },
    { icon: Award, value: '15+', label: 'Years Experience' },
    { icon: Star, value: '4.9/5', label: 'Student Rating' },
  ];

  const teachingApproach = [
    {
      title: 'Personalized Learning',
      description: 'Every student receives a customized learning plan based on their current level, goals, and learning style.'
    },
    {
      title: 'Patient & Supportive',
      description: 'Creating a comfortable environment where students feel confident to learn and make mistakes.'
    },
    {
      title: 'Holistic Education',
      description: 'Beyond recitation, we focus on understanding, spirituality, and practical application in daily life.'
    },
    {
      title: 'Modern Methods',
      description: 'Combining traditional Islamic teaching with modern educational techniques and technology.'
    }
  ];

  return (
    <section id="about" className="py-24 bg-white dark:bg-slate-900 transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main About Section */}
        <div className="grid lg:grid-cols-12 gap-16 items-center mb-24">
          {/* Left Column - Image */}
          <div className="lg:col-span-5 relative">
            <div className="aspect-[4/5] bg-gradient-to-br from-emerald-50 via-white to-green-50 dark:from-slate-800 dark:via-slate-700 dark:to-slate-800 rounded-3xl shadow-2xl overflow-hidden border border-slate-200 dark:border-slate-600 relative animate-fade-in-left">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-600/5 to-green-600/5"></div>
              <div className="w-full h-full flex items-center justify-center relative z-10 p-8">
                <img
                  src="/images/teacher-placeholder.svg"
                  alt="Ustazah Norazah - Professional Al-Quran Teacher"
                  className="w-4/5 h-4/5 object-contain animate-scale-in-bounce animate-stagger-1"
                />
              </div>

              {/* Inspirational Quote Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex items-end justify-center p-6 opacity-0 hover:opacity-100 transition-opacity duration-300">
                <div className="text-center text-white">
                  <p className="text-sm italic leading-relaxed mb-2">
                    "...just my profession, it's my passion and calling."
                  </p>
                  <p className="text-xs font-medium text-white/80">- Ustazah Norazah</p>
                </div>
              </div>

              {/* Professional badge */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-emerald-600 text-white px-2 py-1 rounded-full text-xs font-medium animate-bounce-in animate-stagger-2 text-center">
                Certified Teacher
              </div>
            </div>

            {/* Floating Quote */}
            <div className="absolute -bottom-8 -right-8 bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 max-w-sm border border-slate-100 dark:border-slate-600">
              <p className="text-slate-700 dark:text-slate-300 italic mb-3 text-lg leading-relaxed">
                "Teaching the Quran is not just my profession, it's my passion and calling."
              </p>
              <p className="text-slate-500 dark:text-slate-400 font-medium">- Ustazah Norazah</p>
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="lg:col-span-7">
            <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-8 animate-fade-in-down">
              <span>About Your Teacher</span>
            </div>

            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-slate-100 mb-8 leading-tight animate-fade-in-right animate-stagger-1 text-professional-lg">
              Meet <span className="gradient-text-elegant">Ustazah Norazah</span>
            </h2>

            <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed animate-fade-in-right animate-stagger-2 text-professional">
              Assalamu Alaikum! I am Ustazah Norazah, a dedicated Quran teacher based in Punggol, Singapore.
              With over 15 years of experience in Islamic education, I have had the privilege of guiding
              hundreds of students on their journey to connect with the Holy Quran.
            </p>

            <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-10 leading-relaxed animate-fade-in-right animate-stagger-3 text-professional">
              My teaching philosophy centers on creating a nurturing environment where students of all ages
              can learn at their own pace while developing a deep love and understanding of the Quran.
              I believe that every student is unique, and I tailor my approach to meet their individual needs.
            </p>

            <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-10 leading-relaxed animate-fade-in-right animate-stagger-3 text-professional">
              My passion for the Qur’an has inspired me to dedicate myself fully to improving my recitation and understanding, while also sharing the love for the Qur’an by guiding and inspiring others on their own journeys.
            </p>

            {/* Qualifications */}
            <div className="mb-8">
              <h3 className="text-xl font-bold text-slate-900 dark:text-slate-100 mb-4">Qualifications & Certifications</h3>
              <ul className="space-y-3">
                {qualifications.map((qualification, index) => (
                  <li key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-slate-700 dark:text-slate-300">{qualification}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* CTA */}
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="#contact"
                className="btn-professional text-white px-8 py-3 rounded-lg font-medium btn-hover-lift text-center text-professional"
              >
                Schedule a Meeting
              </a>
              <a
                href="/blog"
                className="bg-gradient-to-r from-emerald-600 to-emerald-700 text-white px-8 py-3 rounded-lg font-medium hover:from-emerald-700 hover:to-emerald-800 transition-all duration-300 btn-hover-lift text-center text-professional shadow-professional"
              >
                Read My Blog
              </a>
              <a
                href="/resources"
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 text-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
              >
                View Resources
              </a>
            </div>
          </div>
        </div>

        {/* Achievements */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {achievements.map((achievement, index) => (
            <div key={index} className={`text-center animate-scale-in-bounce animate-stagger-${index + 4}`}>
              <div className="w-16 h-16 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center mx-auto mb-4 hover:scale-110 transition-transform duration-300">
                <achievement.icon className="w-8 h-8 text-emerald-600 dark:text-emerald-400" />
              </div>
              <div className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-2">{achievement.value}</div>
              <div className="text-slate-600 dark:text-slate-400">{achievement.label}</div>
            </div>
          ))}
        </div>

        {/* Teaching Approach */}
        <div>
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-4">My Teaching Approach</h3>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              I believe in combining traditional Islamic teaching methods with modern educational approaches
              to create an effective and engaging learning experience.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {teachingApproach.map((approach, index) => (
              <div key={index} className="bg-slate-50 dark:bg-slate-800 rounded-xl p-8">
                <h4 className="text-xl font-bold text-slate-900 dark:text-slate-100 mb-4">{approach.title}</h4>
                <p className="text-slate-600 dark:text-slate-300">{approach.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
