# <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> Teacher Website

A professional, SEO-optimized Next.js website for <PERSON><PERSON><PERSON><PERSON>, an experienced female Al-Quran teacher based in Punggol, Singapore.

## 🌟 Features

### 🎯 High-Converting Design
- Professional and aesthetically pleasing design
- Conversion-optimized layout with clear call-to-actions
- Mobile-responsive design for all devices
- Islamic-themed color scheme and patterns

### 📈 SEO Optimized
- Comprehensive meta tags and structured data
- Google Analytics integration
- Optimized sitemap and robots.txt
- Fast loading times with Next.js optimization
- Semantic HTML structure

### 🎓 Service Showcase
- Individual Quran classes
- Group learning sessions
- Online classes via video call
- Specialized Tajweed programs
- Children's Quran programs
- Intensive courses

### 🎁 Lead Magnets
- Free Quran learning guides
- Prayer time schedules for Singapore
- Tajweed reference charts
- Islamic calendars
- Children's activity packs
- Audio and video learning materials

### 💬 Interactive Features
- Intelligent chatbot for student inquiries
- Contact forms with validation
- Newsletter subscription
- Resource download system
- Testimonial carousel

### 🔧 Technical Features
- Built with Next.js 15 and TypeScript
- Tailwind CSS for styling
- React Hook Form for form handling
- Framer Motion for animations
- Responsive design
- Performance optimized

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ustazah-norazah-quran
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` and add your Google Analytics ID:
```
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── resources/         # Resources page
│   ├── services/          # Services page
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   └── sitemap.ts         # Dynamic sitemap
├── components/            # React components
│   ├── About.tsx          # About section
│   ├── ChatBot.tsx        # Interactive chatbot
│   ├── Contact.tsx        # Contact form
│   ├── Footer.tsx         # Site footer
│   ├── GoogleAnalytics.tsx # GA integration
│   ├── Header.tsx         # Navigation header
│   ├── Hero.tsx           # Hero section
│   ├── ResourcesPage.tsx  # Resources page
│   ├── Services.tsx       # Services showcase
│   └── Testimonials.tsx   # Customer testimonials
└── globals.css            # Global styles
```

## 🎨 Customization

### Colors
The website uses a professional color scheme:
- Primary: Blue (#1e40af)
- Secondary: Green (#059669)
- Accent: Orange (#d97706)

### Fonts
- Main font: Inter (Google Fonts)
- Arabic text: Amiri (Google Fonts)

### Content Updates
1. Update contact information in components
2. Replace placeholder images with actual photos
3. Customize service offerings and pricing
4. Add real testimonials and reviews

## 📱 Pages

### Homepage (/)
- Hero section with call-to-action
- Services overview
- About section
- Testimonials
- Contact form

### About (/about)
- Teacher biography
- Qualifications and certifications
- Teaching philosophy
- Student success stories

### Services (/services)
- Detailed service descriptions
- Pricing information
- Booking options

### Resources (/resources)
- Free downloadable materials
- Lead magnet forms
- Newsletter signup

### Contact (/contact)
- Contact form
- Location information
- FAQ section

## 🔧 Configuration

### Google Analytics
Add your Google Analytics ID to `.env.local`:
```
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

### SEO Settings
Update the metadata in `src/app/layout.tsx` with:
- Actual website URL
- Real contact information
- Proper verification codes

### Contact Information
Update contact details in:
- `src/components/Header.tsx`
- `src/components/Footer.tsx`
- `src/components/Contact.tsx`

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Add environment variables
4. Deploy

### Other Platforms
The website can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## 📊 Performance

The website is optimized for:
- Fast loading times
- Mobile responsiveness
- SEO rankings
- Conversion rates
- User experience

## 🤝 Support

For support with this website:
1. Check the documentation
2. Review the code comments
3. Test in development mode
4. Contact the development team

## 📄 License

This project is created for Ustazah Norazah's Al-Quran teaching services.

---

Built with ❤️ for the Muslim community in Singapore.
