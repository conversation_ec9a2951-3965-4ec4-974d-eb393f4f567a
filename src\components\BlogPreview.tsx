import Link from 'next/link';
import { Calendar, Clock, ArrowRight, BookO<PERSON>, Star } from 'lucide-react';

const BlogPreview = () => {
  const featuredPosts = [
    {
      id: 1,
      title: 'Why Choose a Female Quran Teacher in Singapore: Benefits for Women and Children',
      slug: 'female-quran-teacher-singapore-benefits',
      excerpt: 'Discover the unique advantages of learning Quran from a certified female teacher, especially for women and young children in Singapore\'s multicultural environment.',
      category: 'Islamic Education',
      readTime: '6 min read',
      publishDate: '2025-01-15',
      featured: true,
    },
    {
      id: 2,
      title: '7 Common Tajweed Mistakes Beginners Make (And How to Fix Them)',
      slug: 'common-tajweed-mistakes-beginners-fix',
      excerpt: 'Learn about the most frequent Tajweed errors new students make and practical solutions to improve your Quran recitation from day one.',
      category: 'Tajweed',
      readTime: '8 min read',
      publishDate: '2025-01-12',
      featured: true,
    },
    {
      id: 3,
      title: 'Effective Quran Memorization Tips for Children: A Parent\'s Complete Guide',
      slug: 'quran-memorization-tips-children-parents-guide',
      excerpt: 'Proven strategies to help your child memorize Quran effectively while maintaining love for Islamic learning.',
      category: 'Children\'s Education',
      readTime: '10 min read',
      publishDate: '2025-01-10',
      featured: false,
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-emerald-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 islamic-pattern opacity-5 dark:opacity-10"></div>
      
      {/* Dark Mode Islamic Pattern */}
      <div className="absolute inset-0 opacity-0 dark:opacity-15 transition-opacity duration-300">
        <div className="absolute inset-0 star-crescent"></div>
        <div className="absolute top-1/4 right-1/4 w-1/3 h-1/3 islamic-pattern opacity-40"></div>
        <div className="absolute bottom-1/4 left-1/4 w-1/4 h-1/4 mosque-silhouette opacity-30"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center space-x-2 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <BookOpen className="w-4 h-4" />
            <span>Islamic Education Blog</span>
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold text-slate-900 dark:text-slate-100 mb-6 leading-tight">
            Latest <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">Learning Insights</span>
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed">
            Expert tips, Islamic education guidance, and spiritual insights to enhance your Quran learning journey.
          </p>
        </div>

        {/* Featured Posts Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {featuredPosts.map((post, index) => (
            <article
              key={post.id}
              className={`group bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-slate-200 dark:border-slate-700 card-hover-lift animate-fade-in-up animate-stagger-${index + 1}`}
            >
              {/* Post Image/Pattern */}
              <div className="aspect-[16/10] bg-gradient-to-br from-blue-100 to-emerald-100 dark:from-blue-900/30 dark:to-emerald-900/30 relative overflow-hidden">
                <div className="absolute inset-0 islamic-geometric opacity-20"></div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                <div className="absolute top-4 left-4">
                  <span className="bg-emerald-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    {post.category}
                  </span>
                </div>
                {index === 0 && (
                  <div className="absolute top-4 right-4">
                    <div className="flex items-center space-x-1 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      <Star className="w-3 h-3 fill-current" />
                      <span>Featured</span>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Post Content */}
              <div className="p-6">
                <div className="flex items-center space-x-4 text-sm text-slate-500 dark:text-slate-400 mb-4">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(post.publishDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{post.readTime}</span>
                  </div>
                </div>
                
                <h3 className="text-xl font-bold text-slate-900 dark:text-slate-100 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                  {post.title}
                </h3>
                
                <p className="text-slate-600 dark:text-slate-300 mb-6 text-sm leading-relaxed line-clamp-3">
                  {post.excerpt}
                </p>
                
                <Link
                  href={`/blog/${post.slug}`}
                  className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-700 font-semibold group-hover:translate-x-1 transition-transform"
                >
                  <span>Read Article</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </article>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center animate-fade-in-up animate-stagger-4">
          <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-lg border border-slate-200 dark:border-slate-700 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4">
              Explore More Islamic Learning Resources
            </h3>
            <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">
              Discover comprehensive guides on Quran learning, Tajweed mastery, Islamic education, and spiritual growth from your trusted teacher.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/blog"
                className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 btn-hover-lift"
              >
                View All Articles
              </Link>
              <Link
                href="#contact"
                className="bg-emerald-600 text-white px-8 py-3 rounded-xl font-semibold hover:bg-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 btn-hover-lift"
              >
                Start Learning Today
              </Link>
            </div>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-16 bg-gradient-to-r from-emerald-600 to-blue-600 rounded-2xl p-8 text-center text-white animate-fade-in-up animate-stagger-5">
          <h4 className="text-2xl font-bold mb-4">Stay Updated with Weekly Islamic Learning Tips</h4>
          <p className="text-emerald-100 mb-6 max-w-2xl mx-auto">
            Get expert insights on Quran learning, Islamic education, and spiritual guidance delivered to your inbox.
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-6 py-3 rounded-xl text-slate-900 placeholder-slate-500 focus:ring-2 focus:ring-white focus:outline-none"
            />
            <button className="bg-white text-emerald-600 px-8 py-3 rounded-xl font-semibold hover:bg-emerald-50 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BlogPreview;
