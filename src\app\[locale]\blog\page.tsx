'use client';

import Link from 'next/link';
import { Calendar, Clock, ArrowRight, User, ArrowLeft } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const blogPosts = [
  {
    id: 'female-quran-teacher-singapore-benefits',
    title: 'Benefits of Learning from a Female Quran Teacher in Singapore',
    titleMs: 'Faedah Belajar dengan Guru Al-Quran Wanita di Singapura',
    excerpt: 'Discover the unique advantages of Quranic education with female instructors, especially for women and children in Singapore\'s multicultural environment.',
    excerptMs: '<PERSON><PERSON><PERSON> kelebihan unik pendidikan Al-Quran dengan pengajar wanita, terutama untuk wanita dan kanak-kanak dalam persekitaran multikultural Singapura.',
    date: '2024-06-15',
    readTime: '5 min read',
    readTimeMs: '5 min bacaan',
    category: 'Education',
    categoryMs: 'Pendidikan',
    image: '/images/blog/female-teacher.jpg'
  },
  {
    id: 'tajweed-rules-beginners-guide',
    title: 'Tajweed Rules: A Beginner\'s Guide to Beautiful Quran Recitation',
    titleMs: 'Peraturan Tajwid: Panduan Pemula untuk Bacaan Al-Quran yang Indah',
    excerpt: 'Master the art of Quranic recitation with our comprehensive guide to Tajweed rules, perfect for beginners starting their journey.',
    excerptMs: 'Kuasai seni bacaan Al-Quran dengan panduan komprehensif peraturan Tajwid kami, sesuai untuk pemula yang memulakan perjalanan mereka.',
    date: '2024-06-10',
    readTime: '7 min read',
    readTimeMs: '7 min bacaan',
    category: 'Tajweed',
    categoryMs: 'Tajwid',
    image: '/images/blog/tajweed.jpg'
  },
  {
    id: 'quran-memorization-techniques-children',
    title: 'Effective Quran Memorization Techniques for Children',
    titleMs: 'Teknik Hafazan Al-Quran yang Berkesan untuk Kanak-kanak',
    excerpt: 'Proven methods and strategies to help children memorize the Quran effectively while maintaining understanding and love for the holy book.',
    excerptMs: 'Kaedah dan strategi terbukti untuk membantu kanak-kanak menghafaz Al-Quran dengan berkesan sambil mengekalkan pemahaman dan cinta terhadap kitab suci.',
    date: '2024-06-05',
    readTime: '6 min read',
    readTimeMs: '6 min bacaan',
    category: 'Memorization',
    categoryMs: 'Hafazan',
    image: '/images/blog/children-learning.jpg'
  },
  {
    id: 'online-vs-offline-quran-classes',
    title: 'Online vs Offline Quran Classes: Which is Right for You?',
    titleMs: 'Kelas Al-Quran Dalam Talian vs Luar Talian: Mana yang Sesuai untuk Anda?',
    excerpt: 'Compare the benefits and drawbacks of online and offline Quran learning to make the best choice for your educational journey.',
    excerptMs: 'Bandingkan faedah dan kelemahan pembelajaran Al-Quran dalam talian dan luar talian untuk membuat pilihan terbaik bagi perjalanan pendidikan anda.',
    date: '2024-05-30',
    readTime: '4 min read',
    readTimeMs: '4 min bacaan',
    category: 'Learning Methods',
    categoryMs: 'Kaedah Pembelajaran',
    image: '/images/blog/online-learning.jpg'
  },
  {
    id: 'importance-islamic-education-modern-world',
    title: 'The Importance of Islamic Education in the Modern World',
    titleMs: 'Kepentingan Pendidikan Islam dalam Dunia Moden',
    excerpt: 'Explore how Islamic education remains relevant and essential in today\'s globalized world, providing moral guidance and spiritual growth.',
    excerptMs: 'Terokai bagaimana pendidikan Islam kekal relevan dan penting dalam dunia global hari ini, menyediakan bimbingan moral dan pertumbuhan rohani.',
    date: '2024-05-25',
    readTime: '8 min read',
    readTimeMs: '8 min bacaan',
    category: 'Islamic Studies',
    categoryMs: 'Pengajian Islam',
    image: '/images/blog/islamic-education.jpg'
  }
];

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-slate-900 transition-colors duration-300">
      <Header />
      
      {/* Header */}
      <div className="bg-gradient-to-br from-emerald-50 via-white to-emerald-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Latest Articles
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Insights and guidance on Quran learning and Islamic education
            </p>
          </div>
        </div>
      </div>

      {/* Blog Posts Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post) => (
            <article
              key={post.id}
              className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
            >
              {/* Image */}
              <div className="aspect-w-16 aspect-h-9 bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-900 dark:to-emerald-800">
                <div className="w-full h-48 bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-900 dark:to-emerald-800 flex items-center justify-center">
                  <div className="text-emerald-600 dark:text-emerald-400">
                    <User className="w-16 h-16" />
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Category */}
                <div className="flex items-center justify-between mb-3">
                  <span className="inline-block px-3 py-1 text-xs font-semibold text-emerald-600 dark:text-emerald-400 bg-emerald-100 dark:bg-emerald-900 rounded-full">
                    {post.category}
                  </span>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Clock className="w-4 h-4 mr-1" />
                    {post.readTime}
                  </div>
                </div>

                {/* Title */}
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors duration-300">
                  {post.title}
                </h2>

                {/* Excerpt */}
                <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>

                {/* Meta */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(post.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                  
                  <Link
                    href={`/blog/${post.id}`}
                    className="inline-flex items-center text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300 font-medium transition-colors duration-300"
                  >
                    Read More
                    <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
                  </Link>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>

      {/* Back to Home */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="text-center">
          <Link
            href="/"
            className="inline-flex items-center text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300 font-medium transition-colors duration-300"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back to Home
          </Link>
        </div>
      </div>

      <Footer />
    </div>
  );
}
