import type { Metadata } from "next";
import { <PERSON><PERSON>_Ba<PERSON>, <PERSON><PERSON> } from "next/font/google";
import "../globals.css";
import { Analytics } from '@vercel/analytics/react';
import { Toaster } from 'react-hot-toast';
import GoogleAnalytics from '@/components/GoogleAnalytics';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import JsonLd from '@/components/JsonLd';

const baskerville = Libre_Baskerville({
  subsets: ["latin"],
  variable: "--font-baskerville",
  weight: ["400", "700"],
  display: "swap",
});

const amiri = Amiri({
  subsets: ["arabic", "latin"],
  variable: "--font-amiri",
  weight: ["400", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "<PERSON><PERSON><PERSON><PERSON><PERSON> - Al-Quran Teacher in Singapore",
    template: "%s | <PERSON><PERSON><PERSON><PERSON> - <PERSON> Teacher"
  },
  description: "Learn Al-Quran with <PERSON><PERSON><PERSON><PERSON>, an experienced female Quran teacher in Singapore. Offering personalized Quran classes, Tajweed, and Islamic studies for all ages.",
  keywords: [
    "Al-Quran teacher Singapore",
    "Female Quran teacher",
    "Ustazah Norazah",
    "Quran classes Singapore",
    "Tajweed lessons",
    "Islamic studies",
    "Online Quran classes",
    "Quran tuition"
  ],
  authors: [{ name: "Ustazah Norazah" }],
  creator: "Ustazah Norazah",
  publisher: "Ustazah Norazah Quran Academy",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ustazah-norazah.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Ustazah Norazah - Al-Quran Teacher in Singapore",
    description: "Learn Al-Quran with Ustazah Norazah, an experienced female Quran teacher in Singapore. Offering personalized Quran classes, Tajweed, and Islamic studies for all ages.",
    url: 'https://ustazah-norazah.com',
    siteName: 'Ustazah Norazah Quran Academy',
    locale: 'en_SG',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Ustazah Norazah - Al-Quran Teacher in Singapore',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Ustazah Norazah - Al-Quran Teacher in Singapore",
    description: "Learn Al-Quran with Ustazah Norazah, an experienced female Quran teacher in Singapore.",
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};



export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;

  return (
    <html lang={locale} className={`${baskerville.variable} ${amiri.variable}`}>
      <head>
      </head>
      <body className="font-baskerville antialiased bg-white dark:bg-slate-900 text-gray-900 dark:text-slate-100 transition-colors duration-300">
        <GoogleAnalytics />
        <JsonLd />
        <ThemeProvider>
          <LanguageProvider>
            {children}
          </LanguageProvider>
        </ThemeProvider>
        <Toaster position="top-right" />
        <Analytics />
      </body>
    </html>
  );
}
