import { NextRequest, NextResponse } from 'next/server';

// Hadith interface
interface HadithData {
  id: string;
  text: string;
  source: string;
  collection: string;
  book?: string;
  hadithNumber?: string;
  narrator?: string;
  grade?: string;
  language: 'en' | 'ar';
}

// Curated hadith collection for reliability
const curatedHadiths: HadithData[] = [
  {
    id: '1',
    text: 'The Prophet (ﷺ) said: "The best of people are those who learn the Quran and teach it."',
    source: '<PERSON><PERSON><PERSON> <PERSON><PERSON> 5027',
    collection: '<PERSON><PERSON><PERSON> <PERSON><PERSON>',
    book: 'Virtues of the Quran',
    hadithNumber: '5027',
    narrator: '<PERSON><PERSON><PERSON> ibn <PERSON>',
    grade: 'Sahih',
    language: 'en'
  },
  {
    id: '2',
    text: 'The Prophet (ﷺ) said: "Whoever recites a letter from the Book of Allah, he will be credited with a good deed, and a good deed gets a ten-fold reward."',
    source: '<PERSON>i` at-Tirmidhi 2910',
    collection: '<PERSON>i` at-Tirmidhi',
    book: 'Virtues of the Quran',
    hadithNumber: '2910',
    narrator: '<PERSON>\'ud',
    grade: '<PERSON>',
    language: 'en'
  },
  {
    id: '3',
    text: 'The Prophet (ﷺ) said: "Read the Quran, for it will come as an intercessor for its companions on the Day of Resurrection."',
    source: 'Sahih Muslim 804',
    collection: 'Sahih Muslim',
    book: 'Traveller\'s Prayers',
    hadithNumber: '804',
    narrator: 'Abu Umamah',
    grade: 'Sahih',
    language: 'en'
  },
  {
    id: '4',
    text: 'The Prophet (ﷺ) said: "The one who is proficient in the recitation of the Quran will be with the honourable and obedient scribes (angels) and he who recites the Quran and finds it difficult to recite, doing his best to recite it in the best way possible, will have a double reward."',
    source: 'Sahih al-Bukhari 4937',
    collection: 'Sahih al-Bukhari',
    book: 'Virtues of the Quran',
    hadithNumber: '4937',
    narrator: 'Aisha',
    grade: 'Sahih',
    language: 'en'
  },
  {
    id: '5',
    text: 'The Prophet (ﷺ) said: "The hearts rust like iron rusts, and their polish is the remembrance of Allah and the recitation of the Quran."',
    source: 'Shu\'ab al-Iman 1870',
    collection: 'Shu\'ab al-Iman',
    book: 'Faith',
    hadithNumber: '1870',
    narrator: 'Abu Hurairah',
    grade: 'Hasan',
    language: 'en'
  },
  {
    id: '6',
    text: 'The Prophet (ﷺ) said: "Whoever listens to a verse from the Book of Allah, it will be recorded for him as a good deed that is multiplied, and whoever recites a verse, it will be a light for him on the Day of Resurrection."',
    source: 'Musnad Ahmad 22248',
    collection: 'Musnad Ahmad',
    book: 'Musnad',
    hadithNumber: '22248',
    narrator: 'Abdullah ibn Mas\'ud',
    grade: 'Hasan',
    language: 'en'
  },
  {
    id: '7',
    text: 'The Prophet (ﷺ) said: "Make things easy and do not make them difficult, cheer the people up by conveying glad tidings to them and do not repulse them."',
    source: 'Sahih al-Bukhari 6125',
    collection: 'Sahih al-Bukhari',
    book: 'Manners',
    hadithNumber: '6125',
    narrator: 'Anas ibn Malik',
    grade: 'Sahih',
    language: 'en'
  },
  {
    id: '8',
    text: 'The Prophet (ﷺ) said: "The believer is not one who eats his fill while his neighbor goes hungry."',
    source: 'Al-Adab Al-Mufrad 112',
    collection: 'Al-Adab Al-Mufrad',
    book: 'Neighbors',
    hadithNumber: '112',
    narrator: 'Ibn Abbas',
    grade: 'Hasan',
    language: 'en'
  },
  {
    id: '9',
    text: 'The Prophet (ﷺ) said: "A good word is charity."',
    source: 'Sahih al-Bukhari 2989',
    collection: 'Sahih al-Bukhari',
    book: 'Jihad',
    hadithNumber: '2989',
    narrator: 'Abu Hurairah',
    grade: 'Sahih',
    language: 'en'
  },
  {
    id: '10',
    text: 'The Prophet (ﷺ) said: "The most beloved of deeds to Allah are those that are most consistent, even if they are small."',
    source: 'Sahih al-Bukhari 6464',
    collection: 'Sahih al-Bukhari',
    book: 'Invocations',
    hadithNumber: '6464',
    narrator: 'Aisha',
    grade: 'Sahih',
    language: 'en'
  }
];

// Cache management
let cachedHadith: HadithData | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 1 week in milliseconds

function getWeeklyHadith(): HadithData {
  const now = Date.now();
  
  // Check if we have a valid cached hadith
  if (cachedHadith && (now - cacheTimestamp) < CACHE_DURATION) {
    return cachedHadith;
  }
  
  // Get week number to ensure same hadith for the whole week
  const startOfYear = new Date(new Date().getFullYear(), 0, 1);
  const weekNumber = Math.floor((now - startOfYear.getTime()) / (7 * 24 * 60 * 60 * 1000));
  
  // Select hadith based on week number
  const hadithIndex = weekNumber % curatedHadiths.length;
  const selectedHadith = curatedHadiths[hadithIndex];
  
  // Update cache
  cachedHadith = selectedHadith;
  cacheTimestamp = now;
  
  return selectedHadith;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const language = searchParams.get('lang') || 'en';
    const type = searchParams.get('type') || 'weekly';
    const id = searchParams.get('id');

    let hadith: HadithData;

    if (type === 'random') {
      // Get a random hadith
      const randomIndex = Math.floor(Math.random() * curatedHadiths.length);
      hadith = curatedHadiths[randomIndex];
    } else if (id) {
      // Get specific hadith by ID
      const foundHadith = curatedHadiths.find(h => h.id === id);
      if (!foundHadith) {
        return NextResponse.json({
          success: false,
          error: 'Hadith not found',
          message: `No hadith found with ID: ${id}`
        }, { status: 404 });
      }
      hadith = foundHadith;
    } else {
      // Get the weekly hadith (default)
      hadith = getWeeklyHadith();
    }

    // Return the hadith with proper headers
    return NextResponse.json({
      success: true,
      data: hadith,
      timestamp: new Date().toISOString(),
      cacheUntil: new Date(Date.now() + CACHE_DURATION).toISOString(),
      totalHadiths: curatedHadiths.length
    }, {
      headers: {
        'Cache-Control': type === 'weekly' ? 'public, max-age=3600' : 'public, max-age=300', // Cache weekly for 1 hour, others for 5 minutes
        'Content-Type': 'application/json',
      }
    });

  } catch (error) {
    console.error('Error in hadith API:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch hadith',
      message: 'Unable to retrieve hadith at this time'
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      }
    });
  }
}

// Optional: Add POST method for admin to update hadiths
export async function POST(request: NextRequest) {
  try {
    // This could be used for admin functionality to add new hadiths
    // For now, return method not allowed
    return NextResponse.json({
      success: false,
      error: 'Method not implemented',
      message: 'POST method is not yet implemented'
    }, {
      status: 501
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, {
      status: 500
    });
  }
}
