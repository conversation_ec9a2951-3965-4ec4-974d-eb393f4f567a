# Hadith API Documentation

## Overview
The Hadith API provides access to a curated collection of authentic Islamic hadiths for the Al-Quran teaching website. The API is designed to serve a "Hadith of the Week" feature with additional functionality for random hadiths and administrative purposes.

## Base URL
```
/api/hadith
```

## Endpoints

### 1. Get Hadith
**GET** `/api/hadith`

Retrieves a hadith based on the specified parameters.

#### Query Parameters
- `type` (optional): Type of hadith to retrieve
  - `weekly` (default): Returns the same hadith for the entire week
  - `random`: Returns a random hadith from the collection
- `lang` (optional): Language preference (default: `en`)
- `id` (optional): Specific hadith ID to retrieve

#### Examples
```bash
# Get weekly hadith (default)
GET /api/hadith

# Get random hadith
GET /api/hadith?type=random

# Get specific hadith by ID
GET /api/hadith?id=1
```

#### Response Format
```json
{
  "success": true,
  "data": {
    "id": "1",
    "text": "<PERSON> Prophet (ﷺ) said: \"The best of people are those who learn the Quran and teach it.\"",
    "source": "<PERSON><PERSON><PERSON> 5027",
    "collection": "<PERSON><PERSON><PERSON>",
    "book": "Virtues of the Quran",
    "hadithNumber": "5027",
    "narrator": "Uthman ibn Affan",
    "grade": "Sahih",
    "language": "en"
  },
  "timestamp": "2025-01-13T10:30:00.000Z",
  "cacheUntil": "2025-01-20T10:30:00.000Z",
  "totalHadiths": 10
}
```

### 2. List Hadiths
**GET** `/api/hadith/list`

Retrieves a paginated list of all available hadiths.

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of hadiths per page (default: 10)
- `collection` (optional): Filter by hadith collection

#### Examples
```bash
# Get first page with default limit
GET /api/hadith/list

# Get specific page with custom limit
GET /api/hadith/list?page=2&limit=5

# Filter by collection
GET /api/hadith/list?collection=bukhari
```

#### Response Format
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "text": "...",
      "source": "...",
      // ... other hadith fields
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 10,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  },
  "filters": {
    "collection": null
  },
  "timestamp": "2025-01-13T10:30:00.000Z"
}
```

## Error Responses

### 404 - Hadith Not Found
```json
{
  "success": false,
  "error": "Hadith not found",
  "message": "No hadith found with ID: 999"
}
```

### 500 - Server Error
```json
{
  "success": false,
  "error": "Failed to fetch hadith",
  "message": "Unable to retrieve hadith at this time"
}
```

## Caching
- Weekly hadiths are cached for 1 hour
- Random hadiths are cached for 5 minutes
- List endpoints are cached for 5 minutes

## Features
- **Weekly Consistency**: The same hadith is served for an entire week
- **Curated Collection**: All hadiths are from authentic sources (Sahih, Hasan)
- **Detailed Metadata**: Includes source, narrator, grade, and collection information
- **Pagination**: Efficient handling of large hadith collections
- **Error Handling**: Comprehensive error responses with helpful messages

## Collections Included
- Sahih al-Bukhari
- Sahih Muslim
- Jami` at-Tirmidhi
- Musnad Ahmad
- Al-Adab Al-Mufrad
- Shu`ab al-Iman

## Usage in Components
The API is designed to work seamlessly with the `HadithOfTheWeek` React component, which includes:
- Automatic weekly caching
- Refresh functionality for new hadiths
- Share functionality
- Loading states
- Error handling

## Future Enhancements
- Multi-language support (Arabic, Malay)
- Admin interface for hadith management
- Search functionality
- Favorite hadiths feature
- Audio recitation support
