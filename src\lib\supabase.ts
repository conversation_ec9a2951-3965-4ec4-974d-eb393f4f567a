import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://jrttbgnncmgijztukdzl.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpydHRiZ25uY21naWp6dHVrZHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MDU0MDYsImV4cCI6MjA2Nzk4MTQwNn0.DrIMnboLDfL0LcDL5uc7rur2kDi0I9ePp7cyOGfIfB8';

// Debug logging (remove in production)
// console.log('Supabase URL:', supabaseUrl);
// console.log('Supabase Key (first 10 chars):', supabaseAnonKey?.substring(0, 10));

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for our database
export interface ContactLead {
  id?: string;
  full_name: string;
  email: string;
  phone: string;
  service: string;
  student_age?: string;
  preferred_time?: string;
  message: string;
  ip_address?: string;
  user_agent?: string;
  created_at?: string;
  updated_at?: string;
}

// Function to submit contact form data
export async function submitContactForm(data: Omit<ContactLead, 'id' | 'created_at' | 'updated_at'>) {
  try {
    const { data: result, error } = await supabase
      .from('contact_leads')
      .insert([data])
      .select()
      .single();

    if (error) {
      console.error('Error submitting contact form:', error);
      throw new Error(`Failed to submit contact form: ${error.message}`);
    }

    return result;
  } catch (error) {
    console.error('Error in submitContactForm:', error);
    throw error;
  }
}

// Function to get all contact leads (for admin dashboard)
export async function getContactLeads() {
  try {
    const { data, error } = await supabase
      .from('contact_leads')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching contact leads:', error);
      throw new Error('Failed to fetch contact leads');
    }

    return data;
  } catch (error) {
    console.error('Error in getContactLeads:', error);
    throw error;
  }
}

// Function to get contact leads count
export async function getContactLeadsCount() {
  try {
    const { count, error } = await supabase
      .from('contact_leads')
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('Error fetching contact leads count:', error);
      throw new Error('Failed to fetch contact leads count');
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getContactLeadsCount:', error);
    throw error;
  }
}

// Function to export contact leads as CSV
export function exportLeadsToCSV(leads: ContactLead[]) {
  const headers = [
    'ID',
    'Full Name',
    'Email',
    'Phone',
    'Service',
    'Student Age',
    'Preferred Time',
    'Message',
    'Submitted At'
  ];

  const csvContent = [
    headers.join(','),
    ...leads.map(lead => [
      lead.id,
      `"${lead.full_name}"`,
      lead.email,
      lead.phone,
      `"${lead.service}"`,
      `"${lead.student_age || ''}"`,
      `"${lead.preferred_time || ''}"`,
      `"${lead.message.replace(/"/g, '""')}"`,
      lead.created_at ? new Date(lead.created_at).toLocaleString() : ''
    ].join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `contact_leads_${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
