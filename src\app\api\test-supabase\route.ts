import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    console.log('Testing Supabase connection...');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('contact_leads')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('Supabase connection error:', error);
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      }, { status: 500 });
    }

    console.log('Supabase connection successful');
    return NextResponse.json({
      success: true,
      message: 'Supabase connection working',
      leadCount: data || 0,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Test endpoint error:', error);
    return NextResponse.json({
      success: false,
      error: 'Connection test failed',
      details: error
    }, { status: 500 });
  }
}

export async function POST() {
  try {
    console.log('Testing Supabase insert...');
    
    // Test insert with dummy data
    const testData = {
      full_name: 'Test User',
      email: '<EMAIL>',
      phone: '+65 1234 5678',
      service: 'Individual Classes',
      message: 'This is a test message',
      user_agent: 'Test Agent'
    };

    const { data, error } = await supabase
      .from('contact_leads')
      .insert([testData])
      .select()
      .single();

    if (error) {
      console.error('Supabase insert error:', error);
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      }, { status: 500 });
    }

    console.log('Supabase insert successful:', data);
    return NextResponse.json({
      success: true,
      message: 'Test lead inserted successfully',
      data: data,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Test insert error:', error);
    return NextResponse.json({
      success: false,
      error: 'Insert test failed',
      details: error
    }, { status: 500 });
  }
}
