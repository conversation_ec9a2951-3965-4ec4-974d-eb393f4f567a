'use client';

import Link from 'next/link';
import {
  BookOpen,
  MapPin,
  Phone,
  Mail,
  Facebook,
  Instagram,
  MessageCircle,
  Heart
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const Footer = () => {
  const { t, language } = useLanguage();

  const quickLinks = [
    { name: t('nav.home'), href: `/${language}` },
    { name: t('nav.about'), href: `/${language}#about` },
    { name: t('nav.services'), href: `/${language}#services` },
    { name: t('nav.blog'), href: `/${language}/blog` },
    { name: t('nav.contact'), href: `/${language}#contact` },
  ];

  const services = [
    { name: t('services.individual.title'), href: `/${language}/services/individual` },
    { name: t('services.group.title'), href: `/${language}/services/group` },
    { name: t('services.online.title'), href: `/${language}/services/online` },
    { name: t('services.tajweed.title'), href: `/${language}/services/tajweed` },
  ];

  const resources = [
    { name: 'Learning Materials', href: '/resources' },
    { name: 'Prayer Times', href: '/prayer-times' },
    { name: 'Islamic Calendar', href: '/calendar' },
    { name: 'FAQ', href: '/faq' },
  ];

  return (
    <footer className="bg-slate-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 mosque-silhouette opacity-5"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Main Footer Content */}
        <div className="py-20">
          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-12">
            {/* Brand Column */}
            <div className="lg:col-span-1">
              <Link href="/" className="flex items-center space-x-3 mb-8 group">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-600 to-green-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
                  <BookOpen className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold">Ustazah Norazah</h3>
                  <p className="text-slate-400 dark:text-slate-300 font-medium">{t('footer.teacherTitle')}</p>
                </div>
              </Link>

              <p className="text-slate-400 dark:text-slate-300 mb-8 leading-relaxed text-lg">
                {t('footer.footerDescription')}
              </p>

              {/* Social Links */}
              <div className="flex space-x-4">
                <a
                  href="https://facebook.com"
                  className="w-12 h-12 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-emerald-600 transition-all duration-300 hover:scale-110 shadow-lg"
                >
                  <Facebook className="w-6 h-6" />
                </a>
                <a
                  href="https://instagram.com"
                  className="w-12 h-12 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-pink-600 transition-all duration-300 hover:scale-110 shadow-lg"
                >
                  <Instagram className="w-6 h-6" />
                </a>
                <a
                  href="https://wa.me/6588352027"
                  className="w-12 h-12 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-green-600 transition-all duration-300 hover:scale-110 shadow-lg"
                >
                  <MessageCircle className="w-6 h-6" />
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-6">{t('footer.quickLinks')}</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-slate-400 dark:text-slate-300 hover:text-white dark:hover:text-slate-100 transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <h4 className="text-lg font-semibold mb-6">{t('footer.services')}</h4>
              <ul className="space-y-3">
                {services.map((service) => (
                  <li key={service.name}>
                    <Link
                      href={service.href}
                      className="text-slate-400 dark:text-slate-300 hover:text-white dark:hover:text-slate-100 transition-colors duration-200"
                    >
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="text-lg font-semibold mb-6">{t('footer.contact')}</h4>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <MapPin className="w-5 h-5 text-emerald-400 mt-1 flex-shrink-0" />
                  <div>
                    <p className="text-slate-400 dark:text-slate-300">Punggol Central, Singapore</p>
                    <p className="text-sm text-slate-500 dark:text-slate-400">Home visits available</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                  <a
                    href="tel:+6588352027"
                    className="text-slate-400 dark:text-slate-300 hover:text-white dark:hover:text-slate-100 transition-colors duration-200"
                  >
                    +65 8835 2027
                  </a>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                  <a
                    href="mailto:<EMAIL>"
                    className="text-slate-400 dark:text-slate-300 hover:text-white dark:hover:text-slate-100 transition-colors duration-200"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              {/* Teaching Hours */}
              <div className="mt-6 p-4 bg-slate-800 dark:bg-slate-700 rounded-lg">
                <h5 className="font-semibold mb-2">Teaching Hours</h5>
                <div className="text-sm text-slate-400 dark:text-slate-300 space-y-1">
                  <p>Mon-Fri: 2PM - 8PM</p>
                  <p>Sat-Sun: 9AM - 6PM</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="border-t border-slate-800 dark:border-slate-600 py-8">
          <div className="text-center">
            <h4 className="text-xl font-semibold mb-4">Stay Connected</h4>
            <p className="text-slate-400 dark:text-slate-300 mb-6 max-w-2xl mx-auto">
              Subscribe to receive Islamic learning tips, Quran verses, and updates about our classes.
            </p>
            
            <div className="max-w-md mx-auto flex gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-emerald-600 focus:border-transparent text-white placeholder-gray-400"
              />
              <button className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-colors duration-200">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-slate-800 dark:border-slate-600 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-slate-400 dark:text-slate-300 text-sm">
              © 2025 Ustazah Norazah Quran Academy. {t('footer.allRights')}
            </div>

            <div className="flex items-center space-x-1 text-slate-400 dark:text-slate-300 text-sm">
              <span>{t('footer.madeWith')}</span>
              <Heart className="w-4 h-4 text-red-500 fill-current" />
              <span>{t('footer.forCommunity')}</span>
            </div>
            
            <div className="flex space-x-6 text-sm">
              <Link href="/privacy" className="text-slate-400 dark:text-slate-300 hover:text-white dark:hover:text-slate-100 transition-colors duration-200">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-slate-400 dark:text-slate-300 hover:text-white dark:hover:text-slate-100 transition-colors duration-200">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
