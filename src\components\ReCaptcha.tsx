'use client';

import { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';

interface ReCaptchaProps {
  siteKey: string;
  onVerify: (token: string) => void;
  onExpire?: () => void;
  onError?: () => void;
  theme?: 'light' | 'dark';
  size?: 'normal' | 'compact';
}

export interface ReCaptchaRef {
  reset: () => void;
  execute: () => void;
}

declare global {
  interface Window {
    grecaptcha: any;
    onRecaptchaLoad: () => void;
  }
}

const ReCaptcha = forwardRef<ReCaptchaRef, ReCaptchaProps>(({
  siteKey,
  onVerify,
  onExpire,
  onError,
  theme = 'light',
  size = 'normal'
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const widgetId = useRef<number | null>(null);
  const isRendered = useRef<boolean>(false);

  useImperativeHandle(ref, () => ({
    reset: () => {
      if (window.grecaptcha && widgetId.current !== null) {
        try {
          window.grecaptcha.reset(widgetId.current);
        } catch (error) {
          console.error('Error resetting reCAPTCHA:', error);
        }
      }
    },
    execute: () => {
      if (window.grecaptcha && widgetId.current !== null) {
        try {
          window.grecaptcha.execute(widgetId.current);
        } catch (error) {
          console.error('Error executing reCAPTCHA:', error);
        }
      }
    }
  }));

  useEffect(() => {
    const loadRecaptcha = () => {
      if (window.grecaptcha && containerRef.current && !isRendered.current) {
        try {
          // Clear any existing content
          containerRef.current.innerHTML = '';

          widgetId.current = window.grecaptcha.render(containerRef.current, {
            sitekey: siteKey,
            callback: onVerify,
            'expired-callback': onExpire,
            'error-callback': onError,
            theme,
            size
          });

          isRendered.current = true;
        } catch (error) {
          console.error('Error rendering reCAPTCHA:', error);
          onError?.();
        }
      }
    };

    // Cleanup function
    const cleanup = () => {
      if (widgetId.current !== null && window.grecaptcha) {
        try {
          window.grecaptcha.reset(widgetId.current);
        } catch (error) {
          // Ignore cleanup errors
        }
      }
      isRendered.current = false;
      widgetId.current = null;
    };

    if (window.grecaptcha) {
      loadRecaptcha();
    } else {
      // Check if script is already loading
      const existingScript = document.querySelector('script[src*="recaptcha"]');

      if (!existingScript) {
        // Load reCAPTCHA script if not already loaded
        const script = document.createElement('script');
        script.src = 'https://www.google.com/recaptcha/api.js?onload=onRecaptchaLoad&render=explicit';
        script.async = true;
        script.defer = true;

        window.onRecaptchaLoad = loadRecaptcha;

        document.head.appendChild(script);
      } else {
        // Script is already loading, wait for it
        const checkRecaptcha = setInterval(() => {
          if (window.grecaptcha) {
            clearInterval(checkRecaptcha);
            loadRecaptcha();
          }
        }, 100);

        // Cleanup interval after 10 seconds
        setTimeout(() => clearInterval(checkRecaptcha), 10000);
      }
    }

    // Cleanup on unmount
    return cleanup;
  }, [siteKey, onVerify, onExpire, onError, theme, size]);

  return <div ref={containerRef} className="recaptcha-container" />;
});

ReCaptcha.displayName = 'ReCaptcha';

export default ReCaptcha;
