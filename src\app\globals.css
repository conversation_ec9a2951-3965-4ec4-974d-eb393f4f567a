@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #1e40af;
  --primary-dark: #1e3a8a;
  --primary-light: #3b82f6;
  --secondary: #047857;
  --secondary-light: #059669;
  --accent: #dc2626;
  --accent-light: #ef4444;
  --background: #ffffff;
  --foreground: #0f172a;
  --muted: #f8fafc;
  --muted-dark: #f1f5f9;
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Libre Baskerville', 'Baskerville', 'Times New Roman', serif;
  line-height: 1.65;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  letter-spacing: -0.01em;
}

/* Arabic text styling */
.arabic-text {
  font-family: 'Amiri', serif;
  direction: rtl;
  text-align: right;
  line-height: 1.8;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Animation utilities */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out;
}

/* Button hover effects */
.btn-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Professional spacing utilities */
.section-padding {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 640px) {
  .section-padding {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

@media (min-width: 768px) {
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}

/* Responsive container utilities */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: 1536px;
  }
}

/* Enhanced responsive typography */
.heading-xl {
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.1;
  font-weight: 800;
  letter-spacing: -0.025em;
}

.heading-lg {
  font-size: clamp(1.75rem, 4vw, 3rem);
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.heading-md {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  line-height: 1.3;
  font-weight: 600;
  letter-spacing: -0.015em;
}

.text-responsive {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  line-height: 1.6;
}

.text-balance {
  text-wrap: balance;
}

/* Device-specific optimizations */
@media (max-width: 640px) {
  .mobile-optimized {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .mobile-padding {
    padding: 1rem;
  }

  .mobile-margin {
    margin: 0.5rem 0;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-optimized {
    font-size: 1rem;
    line-height: 1.6;
  }

  .tablet-padding {
    padding: 1.5rem;
  }
}

@media (min-width: 1025px) {
  .desktop-optimized {
    font-size: 1.125rem;
    line-height: 1.7;
  }

  .desktop-padding {
    padding: 2rem;
  }
}

/* Islamic pattern backgrounds */
.islamic-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f3f4f6' fill-opacity='0.3'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Enhanced Islamic geometric pattern */
.islamic-geometric {
  background-image:
    repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(59, 130, 246, 0.03) 10px, rgba(59, 130, 246, 0.03) 20px),
    repeating-linear-gradient(-45deg, transparent, transparent 10px, rgba(16, 185, 129, 0.03) 10px, rgba(16, 185, 129, 0.03) 20px);
}

/* Mosque silhouette pattern */
.mosque-silhouette {
  background-image: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23059669' fill-opacity='0.04'%3E%3Cpath d='M40 10c-2 0-4 2-4 4v6h-6c-2 0-4 2-4 4v20h-6c-2 0-4 2-4 4v20h40v-20c0-2-2-4-4-4h-6v-20c0-2-2-4-4-4h-6v-6c0-2-2-4-4-4z'/%3E%3Ccircle cx='40' cy='8' r='2'/%3E%3Cpath d='M30 25c0-5.5 4.5-10 10-10s10 4.5 10 10'/%3E%3C/g%3E%3C/svg%3E");
}

/* Arabic calligraphy inspired pattern */
.arabic-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23065f46' fill-opacity='0.02'%3E%3Cpath d='M20 20c10 0 20 10 20 20s-10 20-20 20-20-10-20-20 10-20 20-20zm40 0c10 0 20 10 20 20s-10 20-20 20-20-10-20-20 10-20 20-20zm20 40c10 0 20 10 20 20s-10 20-20 20-20-10-20-20 10-20 20-20zm-40 0c10 0 20 10 20 20s-10 20-20 20-20-10-20-20 10-20 20-20z'/%3E%3C/g%3E%3C/svg%3E");
}

/* Star and crescent pattern */
.star-crescent {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23fbbf24' fill-opacity='0.05'%3E%3Cpath d='M30 5l2 6h6l-5 4 2 6-5-4-5 4 2-6-5-4h6z'/%3E%3Cpath d='M45 25c-5.5 0-10 4.5-10 10 0 2 0.6 3.9 1.6 5.5 1.5-1.1 2.4-2.9 2.4-4.8 0-3.3-2.7-6-6-6-1.9 0-3.7 0.9-4.8 2.4 1.6 1 3.5 1.6 5.5 1.6 5.5 0 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
}

/* Dark mode Islamic patterns */
@media (prefers-color-scheme: dark) {
  .star-crescent {
    background-image: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2310b981' fill-opacity='0.08'%3E%3Cpath d='M40 8l3 9h9l-7 5 3 9-8-6-8 6 3-9-7-5h9z'/%3E%3Cpath d='M60 35c-7 0-13 6-13 13 0 2.5 0.8 4.8 2.1 6.7 1.9-1.4 3.1-3.7 3.1-6.2 0-4.2-3.4-7.6-7.6-7.6-2.4 0-4.6 1.1-6.1 3 2 1.3 4.4 2 6.9 2 7 0 13-6 13-13z'/%3E%3Cpath d='M20 20l2 4h4l-3 2 1 4-4-2-4 2 1-4-3-2h4z'/%3E%3Cpath d='M65 65l1.5 3h3l-2.5 1.5 1 3-3-1.5-3 1.5 1-3-2.5-1.5h3z'/%3E%3C/g%3E%3C/svg%3E");
  }

  .islamic-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%233b82f6' fill-opacity='0.06'%3E%3Cpath d='M50 10 L60 30 L80 30 L66 42 L72 62 L50 50 L28 62 L34 42 L20 30 L40 30 Z'/%3E%3Ccircle cx='50' cy='50' r='25' fill='none' stroke='%2310b981' stroke-width='1' stroke-opacity='0.1'/%3E%3Cpath d='M25 25 Q50 40 75 25 Q60 50 75 75 Q50 60 25 75 Q40 50 25 25' fill='none' stroke='%233b82f6' stroke-width='0.5' stroke-opacity='0.08'/%3E%3C/g%3E%3C/svg%3E");
  }

  .mosque-silhouette {
    background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2310b981' fill-opacity='0.06'%3E%3Cpath d='M60 20 L65 35 L75 30 L70 45 L85 40 L75 55 L90 50 L80 65 L95 60 L85 75 L100 70 L90 85 L105 80 L95 95 L110 90 L100 105 L115 100 L105 115 L120 110 L110 120 L0 120 L0 110 L10 115 L5 100 L20 105 L15 90 L30 95 L25 80 L40 85 L35 70 L50 75 L45 60 L60 65 L55 50 L70 55 L65 40 L80 45 L75 30 L90 35 L85 20 L100 25 L95 10 L110 15 L105 0 L120 5 L115 -10 L0 -10 L0 0 L15 5 L10 20 L25 15 L30 30 L45 25 L50 40 L65 35 Z'/%3E%3C/g%3E%3C/svg%3E");
  }
}

/* Enhanced Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.8s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-scale-in-bounce {
  animation: scaleInBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-rotate-in {
  animation: rotateIn 0.6s ease-out;
}

/* Staggered animations */
.animate-stagger-1 {
  animation-delay: 0.1s;
}

.animate-stagger-2 {
  animation-delay: 0.2s;
}

.animate-stagger-3 {
  animation-delay: 0.3s;
}

.animate-stagger-4 {
  animation-delay: 0.4s;
}

.animate-stagger-5 {
  animation-delay: 0.5s;
}

.animate-stagger-6 {
  animation-delay: 0.6s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-60px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(60px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleInBounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-200deg);
  }
  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

/* Professional hover effects */
.btn-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Enhanced Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #1e40af 0%, #047857 50%, #1e40af 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease-in-out infinite;
}

.gradient-text-gold {
  background: linear-gradient(135deg, #f59e0b 0%, #dc2626 50%, #f59e0b 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease-in-out infinite;
}

.gradient-text-elegant {
  background: linear-gradient(135deg, #059669 0%, #10b981 25%, #34d399 50%, #6ee7b7 75%, #059669 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 4s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Professional shadows */
.shadow-professional {
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

.shadow-professional-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(0, 0, 0, 0.05),
    0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.shadow-professional-xl {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 0, 0, 0.05),
    0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Enhanced glass morphism */
.glass-morphism {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.5);
}

.glass-morphism-dark {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px 0 rgba(0, 0, 0, 0.37),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced button hover effects */
.btn-hover-lift {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.btn-hover-lift::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-hover-lift:hover::before {
  left: 100%;
}

.btn-hover-lift:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.btn-professional {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4px 15px 0 rgba(5, 150, 105, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-professional:hover {
  background: linear-gradient(135deg, #047857 0%, #059669 100%);
  box-shadow:
    0 8px 25px 0 rgba(5, 150, 105, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Enhanced card hover effects */
.card-hover-lift {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
}

.card-hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 15px 35px rgba(0, 0, 0, 0.1),
    0 5px 15px rgba(0, 0, 0, 0.08);
}

/* Professional text styles */
.text-professional {
  font-weight: 500;
  letter-spacing: -0.025em;
  line-height: 1.5;
}

.text-professional-lg {
  font-weight: 600;
  letter-spacing: -0.03em;
  line-height: 1.4;
}

/* Enhanced focus states */
.focus-professional:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(5, 150, 105, 0.1),
    0 0 0 1px rgba(5, 150, 105, 0.3);
}

/* Refined borders */
.border-professional {
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.border-professional-dark {
  border: 1px solid rgba(71, 85, 105, 0.3);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Professional input styles */
input:focus, textarea:focus, select:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(5, 150, 105, 0.1),
    0 0 0 1px rgba(5, 150, 105, 0.3),
    0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  border-radius: 6px;
  border: 2px solid rgba(248, 250, 252, 0.8);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #cbd5e1, #94a3b8);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.8);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #475569, #64748b);
  border: 2px solid rgba(15, 23, 42, 0.8);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #64748b, #94a3b8);
}

/* Professional selection styles */
::selection {
  background: rgba(30, 64, 175, 0.2);
  color: inherit;
}

.dark ::selection {
  background: rgba(59, 130, 246, 0.3);
  color: inherit;
}

/* Enhanced loading states */
.loading-shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Professional image styles */
img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Enhanced typography */
h1, h2, h3, h4, h5, h6 {
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1;
}

/* Professional link styles */
a {
  text-decoration: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

a:hover {
  text-decoration: none;
}

/* Enhanced button states */
button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  transform: none !important;
}

button:not(:disabled):active {
  transform: translateY(1px) scale(0.98);
}

/* Professional form styles */
.form-group {
  position: relative;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label {
  transform: translateY(-1.5rem) scale(0.875);
  color: #059669;
}

/* Enhanced dark mode transitions */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode text visibility fixes */
.dark {
  color-scheme: dark;
}

.dark body {
  color: #f1f5f9;
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #f1f5f9;
}

.dark p {
  color: #cbd5e1;
}

.dark a {
  color: #94a3b8;
}

.dark a:hover {
  color: #f1f5f9;
}

/* Ensure all text elements have proper dark mode colors */
.dark .text-gray-400 {
  color: #94a3b8 !important;
}

.dark .text-gray-500 {
  color: #64748b !important;
}

.dark .text-gray-600 {
  color: #475569 !important;
}

.dark .text-gray-700 {
  color: #334155 !important;
}

.dark .text-gray-800 {
  color: #1e293b !important;
}

.dark .text-gray-900 {
  color: #0f172a !important;
}

/* Override any remaining gray text classes */
.dark [class*="text-gray"] {
  color: #94a3b8 !important;
}

/* Specific overrides for common gray text classes */
.dark .text-gray-100 {
  color: #f1f5f9 !important;
}

.dark .text-gray-200 {
  color: #e2e8f0 !important;
}

.dark .text-gray-300 {
  color: #cbd5e1 !important;
}

.dark .text-gray-400 {
  color: #94a3b8 !important;
}

.dark .text-gray-500 {
  color: #64748b !important;
}

.dark .text-gray-600 {
  color: #475569 !important;
}

.dark .text-gray-700 {
  color: #334155 !important;
}

.dark .text-gray-800 {
  color: #1e293b !important;
}

.dark .text-gray-900 {
  color: #0f172a !important;
}

/* Override black text */
.dark .text-black {
  color: #f1f5f9 !important;
}

/* Ensure all headings are visible */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #f1f5f9 !important;
}

/* Ensure all paragraphs are visible */
.dark p {
  color: #cbd5e1 !important;
}

/* Ensure all spans are visible */
.dark span {
  color: inherit;
}

/* Ensure all divs inherit proper color */
.dark div {
  color: inherit;
}

/* Ensure proper contrast for all text */
.dark input, .dark textarea, .dark select {
  color: #f1f5f9 !important;
  background-color: #1e293b !important;
}

.dark input::placeholder, .dark textarea::placeholder {
  color: #64748b !important;
}

/* Fix any remaining visibility issues */
.dark label {
  color: #cbd5e1 !important;
}

.dark .text-professional {
  color: #cbd5e1 !important;
}

.dark .text-professional-lg {
  color: #f1f5f9 !important;
}
