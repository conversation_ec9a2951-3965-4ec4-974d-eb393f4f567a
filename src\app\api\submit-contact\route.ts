import { NextRequest, NextResponse } from 'next/server';
import { submitContactForm } from '@/lib/supabase';
import { sendNewLeadNotification } from '@/lib/email';
import { validateContactFormSecurity, sanitizeInput, logSecurityEvent, getClientIP } from '@/lib/security';

export async function POST(request: NextRequest) {
  const ip = getClientIP(request);

  try {
    const body = await request.json();

    // Security validation
    const securityCheck = await validateContactFormSecurity(request, body);
    if (!securityCheck.valid) {
      logSecurityEvent('Form submission blocked', ip, { reason: securityCheck.error });
      return NextResponse.json(
        { error: securityCheck.error },
        {
          status: securityCheck.error?.includes('Too many requests') ? 429 : 400,
          headers: securityCheck.headers
        }
      );
    }

    // Extract and sanitize data
    const { name, email, phone, service, message, studentAge, preferredTime, recaptchaToken } = body;

    // Get user agent for analytics
    const userAgent = request.headers.get('user-agent') || 'Unknown';

    // Prepare sanitized data for Supabase
    const leadData = {
      full_name: sanitizeInput(name),
      email: sanitizeInput(email),
      phone: sanitizeInput(phone),
      service: sanitizeInput(service),
      student_age: studentAge ? sanitizeInput(studentAge) : null,
      preferred_time: preferredTime ? sanitizeInput(preferredTime) : null,
      message: sanitizeInput(message),
      ip_address: ip,
      user_agent: userAgent
    };

    // Log successful security validation
    logSecurityEvent('Form submission accepted', ip, {
      service: leadData.service,
      hasRecaptcha: !!recaptchaToken
    });

    // Submit to Supabase database
    const savedLead = await submitContactForm(leadData);

    // Send email notification (don't block response if email fails)
    try {
      await sendNewLeadNotification(savedLead);
      console.log('Email notification sent successfully');
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Contact form submitted successfully',
      leadId: savedLead.id
    }, {
      headers: securityCheck.headers
    });

  } catch (error) {
    logSecurityEvent('Form submission error', ip, { error: error instanceof Error ? error.message : 'Unknown error' });
    console.error('Error in contact form submission:', error);
    return NextResponse.json(
      {
        error: 'Failed to submit contact form',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      {
        status: 500,
        headers: {
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'DENY',
          'X-XSS-Protection': '1; mode=block',
        }
      }
    );
  }
}
