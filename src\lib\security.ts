import { NextRequest } from 'next/server';

// Rate limiting storage (in production, use Redis or database)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Security configuration
const SECURITY_CONFIG = {
  RATE_LIMIT: {
    MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '10'),
    WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  },
  BLOCKED_IPS: new Set<string>(),
  SUSPICIOUS_PATTERNS: [
    /script/i,
    /<.*>/,
    /javascript:/i,
    /vbscript:/i,
    /onload/i,
    /onerror/i,
    /eval\(/i,
    /document\./i,
    /window\./i,
  ],
};

// Get client IP address
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();
  
  return request.ip || 'unknown';
}

// Rate limiting check
export function checkRateLimit(ip: string): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const key = `rate_limit_${ip}`;
  const limit = rateLimitMap.get(key);
  
  if (!limit || now > limit.resetTime) {
    // Reset or create new limit
    const resetTime = now + SECURITY_CONFIG.RATE_LIMIT.WINDOW_MS;
    rateLimitMap.set(key, { count: 1, resetTime });
    return { 
      allowed: true, 
      remaining: SECURITY_CONFIG.RATE_LIMIT.MAX_REQUESTS - 1, 
      resetTime 
    };
  }
  
  if (limit.count >= SECURITY_CONFIG.RATE_LIMIT.MAX_REQUESTS) {
    return { 
      allowed: false, 
      remaining: 0, 
      resetTime: limit.resetTime 
    };
  }
  
  limit.count++;
  return { 
    allowed: true, 
    remaining: SECURITY_CONFIG.RATE_LIMIT.MAX_REQUESTS - limit.count, 
    resetTime: limit.resetTime 
  };
}

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/vbscript:/gi, '') // Remove vbscript: protocols
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
}

// Check for suspicious content
export function containsSuspiciousContent(text: string): boolean {
  return SECURITY_CONFIG.SUSPICIOUS_PATTERNS.some(pattern => pattern.test(text));
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

// Validate phone format
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[\d\s\-\(\)]{8,20}$/;
  return phoneRegex.test(phone);
}

// Check if IP is blocked
export function isIPBlocked(ip: string): boolean {
  return SECURITY_CONFIG.BLOCKED_IPS.has(ip);
}

// Block an IP address
export function blockIP(ip: string, reason: string): void {
  SECURITY_CONFIG.BLOCKED_IPS.add(ip);
  console.warn(`🚫 IP ${ip} blocked: ${reason}`);
}

// Verify reCAPTCHA token
export async function verifyRecaptcha(token: string): Promise<{ success: boolean; error?: string }> {
  const secretKey = process.env.RECAPTCHA_SECRET_KEY;

  if (!secretKey) {
    console.warn('⚠️ reCAPTCHA secret key not configured - allowing submission');
    return { success: true }; // Allow if not configured (for development)
  }

  // Check if using test keys (always pass in development)
  if (secretKey === '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe') {
    console.log('🧪 Using reCAPTCHA test keys - allowing submission');
    return { success: true };
  }

  // Handle fallback token (when reCAPTCHA fails to load)
  if (token === 'fallback') {
    console.warn('⚠️ reCAPTCHA fallback mode - allowing submission');
    return { success: true };
  }

  if (!token) {
    console.warn('⚠️ No reCAPTCHA token provided');
    return { success: false, error: 'No reCAPTCHA token provided' };
  }

  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${secretKey}&response=${token}`,
    });

    const data = await response.json();

    if (!data.success) {
      const errorCodes = data['error-codes'] || [];
      console.warn('⚠️ reCAPTCHA verification failed:', errorCodes);

      // Provide specific error messages
      if (errorCodes.includes('invalid-input-secret')) {
        return { success: false, error: 'reCAPTCHA configuration error' };
      }
      if (errorCodes.includes('invalid-input-response')) {
        return { success: false, error: 'Invalid reCAPTCHA response' };
      }
      if (errorCodes.includes('timeout-or-duplicate')) {
        return { success: false, error: 'reCAPTCHA expired, please try again' };
      }

      return { success: false, error: 'reCAPTCHA verification failed' };
    }

    return { success: true };
  } catch (error) {
    console.error('❌ reCAPTCHA verification error:', error);
    return { success: false, error: 'reCAPTCHA service unavailable' };
  }
}

// Security headers
export function getSecurityHeaders() {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  };
}

// Comprehensive security check for contact form
export async function validateContactFormSecurity(
  request: NextRequest,
  data: any
): Promise<{ valid: boolean; error?: string; headers?: Record<string, string> }> {
  const ip = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || '';
  
  // Check if IP is blocked
  if (isIPBlocked(ip)) {
    return { 
      valid: false, 
      error: 'Access denied',
      headers: getSecurityHeaders()
    };
  }
  
  // Rate limiting
  const rateLimit = checkRateLimit(ip);
  if (!rateLimit.allowed) {
    const resetTime = new Date(rateLimit.resetTime).toISOString();
    return { 
      valid: false, 
      error: `Too many requests. Try again after ${resetTime}`,
      headers: {
        ...getSecurityHeaders(),
        'X-RateLimit-Limit': SECURITY_CONFIG.RATE_LIMIT.MAX_REQUESTS.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': rateLimit.resetTime.toString(),
      }
    };
  }
  
  // Validate required fields
  if (!data.name || !data.email || !data.phone || !data.service || !data.message) {
    return { 
      valid: false, 
      error: 'Missing required fields',
      headers: getSecurityHeaders()
    };
  }
  
  // Validate email format
  if (!isValidEmail(data.email)) {
    return { 
      valid: false, 
      error: 'Invalid email format',
      headers: getSecurityHeaders()
    };
  }
  
  // Validate phone format
  if (!isValidPhone(data.phone)) {
    return { 
      valid: false, 
      error: 'Invalid phone format',
      headers: getSecurityHeaders()
    };
  }
  
  // Check for suspicious content
  const fieldsToCheck = [data.name, data.email, data.message, data.service];
  for (const field of fieldsToCheck) {
    if (containsSuspiciousContent(field)) {
      blockIP(ip, 'Suspicious content detected');
      return { 
        valid: false, 
        error: 'Invalid content detected',
        headers: getSecurityHeaders()
      };
    }
  }
  
  // Check for bot-like behavior
  if (userAgent.length < 10 || /bot|crawler|spider/i.test(userAgent)) {
    console.warn(`🤖 Potential bot detected: ${ip} - ${userAgent}`);
    // Don't block immediately, but require reCAPTCHA
  }
  
  // Verify reCAPTCHA if provided and configured
  const secretKey = process.env.RECAPTCHA_SECRET_KEY;
  if (secretKey && secretKey !== '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe') {
    // Only require reCAPTCHA in production with real keys
    if (data.recaptchaToken) {
      const recaptchaResult = await verifyRecaptcha(data.recaptchaToken);
      if (!recaptchaResult.success) {
        return {
          valid: false,
          error: recaptchaResult.error || 'Security verification failed. Please try again.',
          headers: getSecurityHeaders()
        };
      }
    } else {
      // In production, require reCAPTCHA token
      return {
        valid: false,
        error: 'Security verification required. Please complete the reCAPTCHA.',
        headers: getSecurityHeaders()
      };
    }
  } else {
    // Development mode - log but allow
    console.log('🧪 Development mode: reCAPTCHA not required');
  }
  
  return { 
    valid: true,
    headers: {
      ...getSecurityHeaders(),
      'X-RateLimit-Limit': SECURITY_CONFIG.RATE_LIMIT.MAX_REQUESTS.toString(),
      'X-RateLimit-Remaining': rateLimit.remaining.toString(),
      'X-RateLimit-Reset': rateLimit.resetTime.toString(),
    }
  };
}

// Log security events
export function logSecurityEvent(event: string, ip: string, details?: any): void {
  const timestamp = new Date().toISOString();
  console.log(`🔒 [${timestamp}] Security Event: ${event} | IP: ${ip}`, details || '');
}

// Clean up old rate limit entries (call periodically)
export function cleanupRateLimitMap(): void {
  const now = Date.now();
  for (const [key, value] of rateLimitMap.entries()) {
    if (now > value.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}
